
import { IJolokiaSimple } from '@jolokia.js/simple'

import { Logger, jolokiaService } from '@hawtio/react'

import { Table, Thead, Tr, Th, Tbody, Td } from '@patternfly/react-table';

import { Button, Flex, FlexItem, Icon, Modal, Split, SplitItem, Tooltip } from '@patternfly/react-core'

import { OutlinedClockIcon, AngleRightIcon, AngleDownIcon, FolderOpenIcon, FolderIcon } from '@patternfly/react-icons'

import { ChartGroup, Chart, ChartLine, ChartAxis, ChartThemeColor, ChartLegend } from '@patternfly/react-charts'

import { useState, useMemo, useCallback, useRef, useEffect } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import './GlobalPlugins.css';


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-counters");

const logPrefix = "** Counters ** ";

log.setLevel(Logger.INFO);

log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Counters
// ********************************************************************************************************
// ********************************************************************************************************

const counterReferences = [

  ["Memory", "CodeTotalSize", true],
  ["Memory", "CodeFreeSize", true],
  ["Memory", "CodeUsedSize", true],

  ["Memory", "StackTotalSize", true],
  ["Memory", "StackFreeSize", true],
  ["Memory", "StackUsedSize", true],

  ["Memory", "DictionaryTotalSize", true],
  ["Memory", "DictionaryFreeSize", true],
  ["Memory", "DictionaryUsedSize", true],

  ["Memory", "HeapTotalSize", true],
  ["Memory", "HeapFreeSize", true],
  ["Memory", "HeapUsedSize", true],

  ["Memory", "ObjectTotalSize", true],
  ["Memory", "ObjectFreeSize", true],
  ["Memory", "ObjectUsedSize", true],

  ["Memory", "RoTotalSize", true],
  ["Memory", "RoFreeSize", true],
  ["Memory", "RoUsedSize", true],

  ["Memory", "TemporaryTotalSize", true],
  ["Memory", "TemporayFreeSize", true],
  ["Memory", "TemporaryUsedSize", true],

  ["Memory", "TrailTotalSize", true],
  ["Memory", "TrailFreeSize", true],
  ["Memory", "TrailUsedSize", true],

  ["Kernel", "ConsoleWaitTime", false],
  ["Kernel", "EvaluateFormulaCount", true],
  ["Kernel", "EvaluateVariableCount", true],
  ["Kernel", "EventWaitTime", false],
  ["Kernel", "FileInputCount", false],
  ["Kernel", "FileInputSize", false],
  ["Kernel", "FileOutputCount", false],
  ["Kernel", "FileOutputSize", false],
  ["Kernel", "FindBetweenCount", false],
  ["Kernel", "FindBetweenDbCount", false],
  ["Kernel", "FindCount", false],
  ["Kernel", "FindDbCount", false],
  ["Kernel", "GetCount", true],
  ["Kernel", "GetDbCount", true],
  ["Kernel", "HeapUsedSize", true],
  ["Kernel", "IdentReclaimCount", true],
  ["Kernel", "IdentReclaimTime", false],
  ["Kernel", "IdentUsedSize", true],
  ["Kernel", "IengCount", false],
  ["Kernel", "LoadFileCount", true],
  ["Kernel", "LoadFileSearchCount", false],
  ["Kernel", "MemoryAllocatedSize", true],
  ["Kernel", "MemoryFreedSize", false],
  ["Kernel", "ObjectReclaimCount", true],
  ["Kernel", "ObjectReclaimTime", false],
  ["Kernel", "ObjectUsedSize", true],
  ["Kernel", "ReclaimTime", false],
  ["Kernel", "RoCodeReclaimCount", false],
  ["Kernel", "RoCodeReclaimTime", false],
  ["Kernel", "RoCodeUsedSize", true],
  ["Kernel", "RunningTime", false],
  ["Kernel", "RwCodeReclaimCount", true],
  ["Kernel", "RwCodeReclaimTime", true],
  ["Kernel", "RwCodeUsedSize", true],
  ["Kernel", "SendCount", true],
  ["Kernel", "SetCount", true],
  ["Kernel", "SetDbCount", false],
  ["Kernel", "StacksReclaimCount", true],
  ["Kernel", "StacksReclaimTime", true],
  ["Kernel", "TemporaryReclaimCount", true],
  ["Kernel", "TemporaryReclaimTime", true],
  ["Kernel", "TemporaryUsedSize", true],
  ["Kernel", "TotalTime", false],
  ["Kernel", "TrailUsedSize", true],
  ["Kernel", "TrxPushContextCount", false],
  ["Kernel", "TrxToDb", true],
  ["Kernel", "XsendCount", true],

  ["SQLAPI", "ociAttrGetCount", true],
  ["SQLAPI", "ociAttrSetCount", true],
  ["SQLAPI", "ociBindByNameCount", true],
  ["SQLAPI", "ociBindByPosCount", true],
  ["SQLAPI", "ociBindDynamicCount", true],
  ["SQLAPI", "ociDefineByPosCount", true],
  ["SQLAPI", "ociDefineDynamicCount", true],
  ["SQLAPI", "ociDescribeAnyCount", true],
  ["SQLAPI", "ociDescriptorAllocCount", true],
  ["SQLAPI", "ociDescriptorFreeCount", true],
  ["SQLAPI", "ociEnvInitCount", true],
  ["SQLAPI", "ociHandleAllocCount", true],
  ["SQLAPI", "ociHandleFreeCount", true],
  ["SQLAPI", "ociInitializeCount", true],
  ["SQLAPI", "ociLobGetLengthCount", true],
  ["SQLAPI", "ociLobReadCount", true],
  ["SQLAPI", "ociLobTrimCount", true],
  ["SQLAPI", "ociLobWriteCount", true],
  ["SQLAPI", "ociLogoffCount", true],
  ["SQLAPI", "ociLogonCount", true],
  ["SQLAPI", "ociParamGetCount", true],
  ["SQLAPI", "ociStmtExecuteCount", true],
  ["SQLAPI", "ociStmtFetchCount", true],
  ["SQLAPI", "ociStmtPrepareCount", true],
  ["SQLAPI", "ociTransCommitCount", false],
  ["SQLAPI", "ociTransDetachCount", false],
  ["SQLAPI", "ociTransForgetCount", false],
  ["SQLAPI", "ociTransPrepareCount", false],
  ["SQLAPI", "ociTransRollbackCount", true],
  ["SQLAPI", "ociTransStartCount", true],
  ["SQLAPI", "totalDbmsCount", true],

  ["SQLDLL", "allocatedArrayBytesCount", true],
  ["SQLDLL", "allocatedCursEBytesCount", true],
  ["SQLDLL", "allocatedCursSBytesCount", true],
  ["SQLDLL", "allocatedDynamBytesCount", true],
  ["SQLDLL", "allocatedFetchBytesCount", true],
  ["SQLDLL", "allocatedSubstBytesCount", true],
  ["SQLDLL", "allocatedTotalBytesCount", true],
  ["SQLDLL", "availableExecCursorCount", true],
  ["SQLDLL", "availableSelectCursorCount", true],
  ["SQLDLL", "commitCount", true],
  ["SQLDLL", "commitTime", false],
  ["SQLDLL", "emptyAllCount", true],
  ["SQLDLL", "emptyAllTime", false],
  ["SQLDLL", "execNewCount", true],
  ["SQLDLL", "execNewTime", false],
  ["SQLDLL", "execReuseCount", true],
  ["SQLDLL", "execReuseTime", false],
  ["SQLDLL", "fetchArrayCount", true],
  ["SQLDLL", "fetchArrayTime", false],
  ["SQLDLL", "fetchOneCount", true],
  ["SQLDLL", "fetchOneTime", false],
  ["SQLDLL", "logoffCount", false],
  ["SQLDLL", "logoffTime", false],
  ["SQLDLL", "logonCount", false],
  ["SQLDLL", "logonTime", false],
  ["SQLDLL", "procedureCount", true],
  ["SQLDLL", "procedureTime", false],
  ["SQLDLL", "releaseAllCount", true],
  ["SQLDLL", "releaseAllTime", true],
  ["SQLDLL", "releaseOneCount", true],
  ["SQLDLL", "releaseOneTime", true],
  ["SQLDLL", "requestFetchNewCount", true],
  ["SQLDLL", "requestFetchNewTime", true],
  ["SQLDLL", "requestFetchReuseCount", true],
  ["SQLDLL", "requestFetchReuseTime", true],
  ["SQLDLL", "requestNewCount", true],
  ["SQLDLL", "requestNewTime", true],
  ["SQLDLL", "requestReuseCount", true],
  ["SQLDLL", "requestReuseTime", true],
  ["SQLDLL", "rollbackCount", true],
  ["SQLDLL", "rollbackTime", true],
  ["SQLDLL", "totalCCount", true],
  ["SQLDLL", "totalCTime", true]
]

type Props = {
  isModal?        : boolean,
  serverPoolName? : string;
  serverName?     : string;
  groupName?      : string;
  counterName?    : string
};

type CountersItemsProps = {
  expandedRows? : Record<string, boolean>;
  toggleRow?    : (k: string) => void;
};

type CounterItem = {
  serverPoolName? : string;
  groupName?      : string;
  counterName?    : string;
  [key: string]   : any;
};

type CounterTree = {
  [serverPoolName: string]: {
    [groupName: string]: {
      [counterName: string]: CounterItem[];
    };
  };
};

const Counters: React.FunctionComponent<Props> = (props: Props) => {

  const propIsModal         = props.isModal         ? props.isModal : false;
  const propServerPoolName  = props.serverPoolName  ? props.serverPoolName : null;
  const propServerName      = props.serverName      ? props.serverName : null;
  const propGroupName       = props.groupName       ? props.groupName : null;
  const propCounterName     = props.counterName     ? props.counterName : null;

  const maxHistory = 100;

  type CountersType = {
    retrievedData : any,
    computedData  : any,
    historyData   : any;
  }

  // ********************************************************************************************************

  const [counters, setCounters] = useState<CountersType>({
    retrievedData: false,
    computedData: [],
    historyData: []
  })

  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRow = useCallback((key: string) => {
    setExpandedRows(prev => ({ ...prev, [key]: !prev[key] }));
  }, []);

  const previousCounterValues = useRef<{ [key: string]: any }>({});

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia: IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  const adminPath = "com.csc.gtmonitor.*:type=Hawtio";

  const retrieveCountersSignature = "retrieveCounters(java.lang.String,java.lang.String,java.lang.String,java.lang.String)";

  async function retrieveCounters(): Promise<any> {
    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);

        if (response != null && response.length > 0) {
          const mbean = response[0];

          log.debug(logPrefix, "retrieveCounters, args: ", propServerPoolName, " / ", propServerName, " / ", propGroupName, " / ", propCounterName);

          const result = await jolokia.execute(mbean, retrieveCountersSignature, propServerPoolName, propServerName, propGroupName, propCounterName);

          log.debug(logPrefix, "retrieveCounters, result: ", result);

          const json: any = (result === null) ? null : JSON.parse(result.toString());

          if (json !== null)
            log.debug(logPrefix, "retrieveCounters, json: ", json);
          else
            log.debug(logPrefix, "retrieveCounters, cannot retrieve counters");

          return json;
        }
      }
      catch (e) {
        log.error(logPrefix, "retrieveCounters, exception: ", e);
      }
    }

    return null;
  }

  async function retrieveData(counters: any): Promise<any> {
    if (counters !== undefined) {
      counters.retrievedData = await retrieveCounters();
      if (counters.retrievedData == null)
        log.error(logPrefix, "retrieveData, cannot retrieve counters");
      else
        log.debug(logPrefix, "retrieveData, counters retrieved: " + counters.retrievedData);
    }
    else
      log.error(logPrefix, "retrieveData, countres is undefined");
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  async function computeCounters(): Promise<any> {
    await refreshJolokia();

    try {
      let countersNew = { ...counters };

      await retrieveData(countersNew);

      log.debug(logPrefix, "computeCounters, counters retrieved: " + countersNew);

      countersNew.computedData = countersNew.retrievedData;

      let historyData = countersNew.historyData;

      historyData.forEach((counter: any) => {
        counter.values.shift();
        counter.values.push(null);
      });

      const serverPools = Object.entries(countersNew.retrievedData);
      serverPools.forEach((serverPool: any) => {
        const serverPoolName: any = serverPool[0];
        const servers: any = Object.entries(serverPool[1]);
        servers.forEach((server: any) => {
          const serverName: any = server[0];
          const serverCounterGroups: any = Object.entries(server[1]);
          serverCounterGroups.forEach((serverCounterGroup: any) => {
            const serverCounterGroupName: any = serverCounterGroup[0];
            const serverCounterGroupCounters: any = Object.entries(serverCounterGroup[1]);
            serverCounterGroupCounters.forEach((serverCounterGroupCounter: any) => {
              const serverCounterGroupCounterName: any = serverCounterGroupCounter[0];
              const serverCounterGroupCounterValue: any = serverCounterGroupCounter[1];
              const index = historyData.findIndex((data: any) => data.serverPoolName === serverPoolName && data.serverName === serverName && data.groupName === serverCounterGroupName && data.counterName === serverCounterGroupCounterName);
              if (index !== -1)
                historyData[index].values[maxHistory - 1] = serverCounterGroupCounterValue;
              else {
                const values = Array(maxHistory).fill(null);
                values[maxHistory - 1] = serverCounterGroupCounterValue;
                historyData.push({ serverPoolName: serverPoolName, serverName: serverName, groupName: serverCounterGroupName, counterName: serverCounterGroupCounterName, values: values })
              }
            })
          })
        })
      })

      historyData = historyData.filter((counter: any) =>
        counter.values[counter.values.length - 1] !== null
        && counterReferences.findIndex((counterReference) => counterReference[0] === counter.groupName && counterReference[1] === counter.counterName && counterReference[2]) > - 1                          // check if counter is a displayable counter
        && counter.values.findIndex((value: any) => value !== null && value !== 0) > -1                                                                                                                     // check if counter has at least a valid value
        //        && counter.values.slice(counter.values.length - historyTailSize, counter.values.length - 2).findIndex((value : any) => value !== null && value !== counter.values[counter.values.length - 1]) > -1   // check if at least one of the historyTailSize tail values is different from latest value
      );

      historyData.sort((counterA: any, counterB: any) => {
        if (counterA.serverPoolName < counterB.serverPoolName) return -1;
        if (counterA.serverPoolName > counterB.serverPoolName) return 1;
        if (counterA.groupName < counterB.groupName) return -1;
        if (counterA.groupName > counterB.groupName) return 1;
        if (counterA.counterName < counterB.counterName) return -1;
        if (counterA.counterName > counterB.counterName) return 1;
        if (counterA.serverName < counterB.serverName) return -1;
        if (counterA.serverName > counterB.serverName) return 1;
        return 0;
      })

      countersNew.historyData = historyData;

      setCounters(countersNew);

    } catch (e) { }
  }

  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying Counters *************************************************************");


  const min = (values: any) => values.reduce((minvalue: any, value: any) => (value != null && value < minvalue ? value : minvalue), Infinity);
  const max = (values: any) => values.reduce((maxvalue: any, value: any) => (value != null && value > maxvalue ? value : maxvalue), -Infinity);

  const CountersItems = (props: CountersItemsProps) => {

    const { expandedRows = {}, toggleRow } = props;
    const showServerNameInstead = !!propServerName;
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);

    const historyData = useMemo(() => counters.historyData || [], [counters.historyData]);

    useEffect(() => {
      const handleResize = () => {
        setWindowWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    const chartHeight         = 90;
    const chartLabelsWidth    = 100;
    const availableChartWidth = Math.floor(windowWidth * 0.55) - chartLabelsWidth - 20; // 20px for padding
    const chartWidth          = Math.max(400, availableChartWidth);

    const chartLegendRows     = 2;
    const chartLegendHeight   = chartLegendRows * 20;
    const chartLegendWidth    = chartWidth;
    const chartLegendGutter   = 10;
    const chartLegendX        = chartLabelsWidth;

    const chartHeightPx       = chartHeight + "px";
    const chartWidthPx        = chartWidth + "px";
    const chartLegendHeightPx = chartLegendHeight + 'px';
    const chartLegendWidthPx  = chartLegendWidth + 'px';

    const chartTitleFontSize  = 12;
    const chartLabelsFontSize = 10;

    const chartLineStyle      = { data: { strokeWidth: 2, strokeOpacity: 0.8 } };

    const buildTree = (data: CounterItem[]): CounterTree => {
      const tree: CounterTree = {};
      data.forEach((item) => {
        const { serverPoolName, groupName, counterName } = item;
        if (!serverPoolName || !groupName|| !counterName) {
          return;
        }
        
        if (!tree[serverPoolName]) {
          tree[serverPoolName] = {};
        }
        if (!tree[serverPoolName][groupName]) {
          tree[serverPoolName][groupName] = {};
        }
        if (!tree[serverPoolName][groupName][counterName]) {
          tree[serverPoolName][groupName][counterName] = [];
        }
        tree[serverPoolName][groupName][counterName].push(item);
      });
      return tree;
    };

    const treeData = buildTree(historyData);

    /////////////// Expand / Collapse 
    const getChildKeys = (node: any, parentKey: string): string[] => {
      let keys: string[] = [];

      Object.entries(node).forEach(([key, value]) => {
        const currentKey = `${parentKey}-${key}`;
        keys.push(currentKey);

        if (typeof value === 'object' && !Array.isArray(value)) {
          keys = keys.concat(getChildKeys(value, currentKey));
        }
      });

      return keys;
    };

    const getDirectChildKeys = (node: any, parentKey: string): string[] => {
      return Object.keys(node).map(key => `${parentKey}-${key}`);
    };

    const handleExpandCollapse = (parentKey: string, node: any, expand: boolean, fullExpand: boolean = true) => {
      let childKeys: string[] = [];

      if (fullExpand) {
        childKeys = getChildKeys(node, parentKey);
      } else {
        childKeys = getDirectChildKeys(node, parentKey);
      }

      const newExpandedRows = { ...expandedRows };

      childKeys.forEach(key => {
        newExpandedRows[key] = expand;
      });

      newExpandedRows[parentKey] = expand;

      setExpandedRows(newExpandedRows);
    };

    const areDirectChildrenExpanded = (parentKey: string, node: any) => {
      const directChildKeys = getDirectChildKeys(node, parentKey);
      return directChildKeys.every(key => expandedRows[key]);
    };

    const areAllChildrenExpanded = (parentKey: string, node: any) => {
      const childKeys = getChildKeys(node, parentKey);
      return childKeys.every(key => expandedRows[key]);
    };

    const renderRows = (data: any, parentKey = '', treeLevel = 0) => {
      const rows: any[] = [];

      Object.entries(data).forEach(([key, value]) => {
        const currentKey = parentKey ? `${parentKey}-${key}` : key;
        const isExpanded = expandedRows[currentKey] || false;

        const cells = Array(5).fill(null).map((_, i) => (
          <Td key = {`${currentKey}-${i}`}>{i === treeLevel ? key : ''}</Td>
        ));

        const isCounterRow = Array.isArray(value) && currentKey.split('-').pop() === key;

        if (isCounterRow) {
          const allLastValues = value
            .map((item: any) => item.values[item.values.length - 1])
            .filter((v: any) => typeof v === 'number');

          let formattedLastCounterValues = '—';
          let isValueChanged = false;

          if (allLastValues.length > 0) {
            const minLastValue = Math.min(...allLastValues);
            const maxLastValue = Math.max(...allLastValues);

            formattedLastCounterValues = (minLastValue === maxLastValue) ? minLastValue.toLocaleString('en-US') : `${minLastValue.toLocaleString('en-US')} .. ${maxLastValue.toLocaleString('en-US')}`;

            const previousValues = previousCounterValues.current[currentKey];

            if (!previousValues) {
              previousCounterValues.current[currentKey] = { minLastValue, maxLastValue };
            } else if (previousValues.minLastValue !== minLastValue || previousValues.maxLastValue !== maxLastValue) {
              isValueChanged = true;
              previousCounterValues.current[currentKey] = { minLastValue, maxLastValue };
            }
          }

          cells[3] = (
            <Td key = {`${currentKey}-val`} className = 'td-last-value'>
              {isValueChanged ? <strong>{formattedLastCounterValues}</strong> : formattedLastCounterValues}
            </Td>
          );
        }

        const isAllExpanded = treeLevel === 0
          ? areDirectChildrenExpanded(currentKey, value)
          : areAllChildrenExpanded(currentKey, value);

          
        cells[treeLevel] = (
          <Td key={`${currentKey}-${treeLevel}`} style={{ cursor: 'pointer' }}>
            <Split style={{ alignItems: 'center' }}>
              <SplitItem isFilled>
                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                  {typeof value === 'object' && value !== null && Object.keys(value).length > 0 ? (
                    <span style={{ marginTop: '3px' }}>
                      {isExpanded ? <AngleDownIcon /> : <AngleRightIcon />}
                    </span>
                  ) : null}
                  <span style={{ marginTop: '3px', marginLeft: '5px' }}>{key}</span>
                </span>
              </SplitItem>
              {typeof value === 'object' && value !== null && Object.keys(value).length > 0 && !Array.isArray(value) && (
                <SplitItem>
                  <Button
                    size="sm"
                    variant="link"
                    onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                      e.stopPropagation();
                      const fullExpand = treeLevel !== 0;
                      handleExpandCollapse(currentKey, value, !isAllExpanded, fullExpand);
                    }}
                    style={{ marginLeft: '10px' }}
                  >
                    {isAllExpanded ? (
                      treeLevel === 0
                        ? <FolderOpenIcon title="Collapse All Counters" />
                        : <FolderOpenIcon title="Collapse All Graphics" />
                    ) : (
                      treeLevel === 0
                        ? <FolderIcon title="Expand All Counters" />
                        : <FolderIcon title="Expand All Graphics" />
                    )}
                  </Button>
                </SplitItem>
              )}
            </Split>
          </Td>
        );

        rows.push(
          <Tr
            key     = {currentKey}
            onClick = {() => toggleRow && toggleRow(currentKey)}
            style   = {{ cursor: 'pointer' }}
          >
            {cells}
          </Tr>
        );

        if (treeLevel === 0) {
          const serverNames: string[] = [];

          const collectServerNames = (obj: any) => {
            if (Array.isArray(obj)) {
              obj.forEach(item => {
                if (item.serverName) serverNames.push(item.serverName);
              });
            } else if (typeof obj === 'object' && obj !== null) {
              Object.values(obj).forEach(collectServerNames);
            }
          };

          collectServerNames(value);

          const uniqueServerNames =  Array.from(new Set(serverNames));
          const showServerNameInstead = !!propServerName;
          

          if (!showServerNameInstead) {
            const legendData = uniqueServerNames.map((name, idx) => ({ name, key: `${currentKey}-legend-${idx}` }));

            cells[4] = (
              <Td tabIndex = {-1} key = {`${currentKey}-legend`} style = {{ alignContent: "start", fontSize: "inherit", padding: "5px 0px 0px 0px" }}>
                <div style = {{ alignContent: "left", height: chartLegendHeightPx, width: chartLegendWidthPx }}>
                  <ChartLegend
                    data        = {legendData}
                    orientation = "vertical"
                    x           = {chartLegendX}
                    y           = {0}
                    height      = {chartLegendHeight}
                    width       = {chartLegendWidth}
                    themeColor  = {ChartThemeColor.multiOrdered}
                    gutter      = {chartLegendGutter}
                    itemsPerRow = {chartLegendRows}
                    style       = {{
                              title: { fontSize: chartTitleFontSize, fill: '#330072' },
                              labels: { fontSize: chartLabelsFontSize, fill: '#330072' }
                            }}
                  />
                </div>
              </Td>
            );
          }
        }

        if (isExpanded) {
          if (Array.isArray(value)) {
            let allValues   : any[] = [];
            let chartGroup  : any[] = [];
            let chartLegend : any[] = [];

            value.forEach((counter, i) => {
              const counterValuesNotNull = counter.values?.map((y: any, x: any) => ({ x, y })).filter((v: any) => v.y != null);
              const counterValues = counter.values;
              if (!counterValuesNotNull || counterValuesNotNull.length === 0) return;

              allValues = allValues.concat(counterValuesNotNull);

              chartGroup.push(
                <ChartLine
                  key           = {`${currentKey}-line-${i}`}
                  interpolation = "monotoneX"
                  data          = {counterValues}
                  style         = {chartLineStyle}
                />
              );

              chartLegend.push({ name: counter.serverName });
            });

            if (chartGroup.length > 0) {
              const yValues   = allValues.map(v => v.y);
              const minY      = min(yValues);
              const maxY      = max(yValues);
              const chartMinY = minY === 0 ? 0 : (minY < 10 ? minY - 1 : Math.round(0.9 * minY));
              const chartMaxY = maxY === 0 ? 1 : (maxY < 10 ? maxY + 1 : Math.round(1.1 * maxY));

              rows.push(
                <Tr key = {`${currentKey}-chart`}>
                  <Td tabIndex={-1} style={{ alignContent: "start", fontSize: "inherit", padding: "0px 0px 0px 0px" }}></Td>
                  <Td tabIndex={-1} style={{ alignContent: "start", fontSize: "inherit", padding: "0px 0px 0px 0px" }}></Td>
                  <Td tabIndex={-1} style={{ alignContent: "start", fontSize: "inherit", padding: "0px 0px 0px 0px" }}></Td>
                  <Td tabIndex={-1} style={{ alignContent: "start", fontSize: "inherit", padding: "0px 0px 0px 0px" }}></Td>
                  <Td tabIndex={-1} style={{ alignContent: "start", fontSize: "inherit", padding: "5px 0px 0px 0px" }}>
                    <div style = {{ alignContent: "left", height: chartHeightPx, width: chartWidthPx }}>
                      <Chart
                        minDomain   = {{ y: chartMinY }}
                        maxDomain   = {{ y: chartMaxY }}
                        height      = {chartHeight}
                        width       = {chartWidth}
                        padding     = {{ top: 5, bottom: 5, left: chartLabelsWidth, right: 5 }}
                        themeColor  = {ChartThemeColor.multiOrdered}
                      >
                        <ChartAxis
                          tickCount   = {10}
                          orientation = "bottom"
                          tickFormat  = {() => ""}
                          style       = {{
                                    axis: { stroke: "#330072", strokeWidth: 1, strokeOpacity: 0.5 },
                                    axisLabel: { fill: "#330072" },
                                    ticks: { stroke: "#330072", strokeWidth: 1, strokeOpacity: 0.5 },
                                    tickLabels: { fontFamily: "inherit", fontStyle: "normal", fontWeight: "lighter", fontSize: chartLabelsFontSize, fill: "#330072" },
                                    grid: { stroke: "#330072", strokeWidth: 1, strokeOpacity: 0.5 }
                                  }}
                        />
                        <ChartAxis dependentAxis
                          style = {{
                              axis: { stroke: "#330072", strokeWidth: 1, strokeOpacity: 0.5 },
                              axisLabel: { fill: "#330072" },
                              ticks: { stroke: "#330072", strokeWidth: 1, strokeOpacity: 0.5 },
                              tickLabels: { fontFamily: "inherit", fontStyle: "normal", fontWeight: "lighter", fontSize: chartLabelsFontSize, fill: "#330072" },
                            }}
                        />
                        <ChartGroup>{chartGroup}</ChartGroup>
                      </Chart>
                    </div>
                  </Td>
                </Tr>
              );
            }
          }
          else {
            rows.push(...renderRows(value, currentKey, treeLevel + 1));
          }
        }
      });

      return rows;
    };

    return (
      <Table
        tabIndex  = {-1}
        className = "counters-custom-table"
        isTreeTable
        variant   = "compact"
        borders   = {false}
        isStriped = {true}
        isStickyHeader
        style     = {{ width: '100%', position: 'relative' }}
      >
        <Thead tabIndex = {-1}>
          <Tr tabIndex = {-1} style = {{ fontSize: "inherit" }}>
            <Th tabIndex = {-1} style = {{ width: "15%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 25px", whiteSpace: 'normal' }}>
              {showServerNameInstead ? "Server Name" : "Server Pool"}
            </Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Counter Group</Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "left", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Counter</Th>
            <Th tabIndex = {-1} style = {{ width: "10%", textAlign:  "right", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}>Last Values</Th>
            <Th tabIndex = {-1} aria-label = "Chart column" style = {{ width: "55%", fontSize: "inherit", padding: "0px 0px 0px 10px", whiteSpace: 'normal' }}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {renderRows(treeData)}
        </Tbody>
      </Table>
    );
  };

  const title = !propIsModal
    ? "Counters"
    : propServerName
      ? propServerName + ' Counters'
      : propServerPoolName
        ? propServerPoolName + ' Counters'
        : "Counters";

  return (
    <GraphTalkComponent key = "Counters" tabIndex = {-1} title = {title} onCompute = {computeCounters}>
      <GraphTalkComponentDiv hasScroll>
        <CountersItems expandedRows = {expandedRows} toggleRow = {toggleRow}
        />
      </GraphTalkComponentDiv>
    </GraphTalkComponent>
  );
}

// ********************************************************************************************************

export const CountersButtonModal: React.FunctionComponent<Props> = (props) => {

  const [isSystemCountersOpen, setIsSystemCountersOpen] = useState(false);

  const handleSystemCountersClick = () => {
    setIsSystemCountersOpen(true);
  }

  const handleSystemCountersClose = () => {
    setIsSystemCountersOpen(false);
  }

  return (
    <Flex tabIndex = {-1}>
      <FlexItem tabIndex = {-1} onClick = {(e : any) => {e.stopPropagation();}}>
        <Tooltip content = {"Counters"}>
          <Button
            tabIndex  = {0}
            variant   = "plain" 
            size      = "default"
            onClick   = {handleSystemCountersClick}
            icon      = {<Icon status = "info" iconSize = "md" style = {{padding: "0px 0px 0px 0px"}}><OutlinedClockIcon/></Icon>}
            style     = {{padding: "0px 0px 0px 0px"}}
          />
        </Tooltip>
        <Modal
          isOpen            = {isSystemCountersOpen}
          onClose           = {handleSystemCountersClose}
          width             = "100%"
          height            = "100%"
          hasNoBodyWrapper  = {true}
          className         = "dialog"
          aria-label        = "CountersButtonModal"
        >
          <Counters isModal = {true} {...props}/>
        </Modal>
      </FlexItem>
    </Flex>
  )
}


// ********************************************************************************************************

export default Counters;