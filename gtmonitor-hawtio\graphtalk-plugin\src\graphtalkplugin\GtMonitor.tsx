
import { configManager, Hawtconfig, AboutProductInfo, Logger, jolokiaService, userService } from '@hawtio/react'

import { ReadResponseValue } from 'jolokia.js';
import { IJolokiaSimple } from '@jolokia.js/simple'

import {
  Button,
  Card, CardHeader, CardTitle, CardExpandableContent, CardBody,
  Flex, FlexItem,
  Grid, GridItem,
  Label,
  KeyTypes,
  Split, SplitItem,
  Stack, StackItem,
  Text, TextContent, TextVariants,
  Truncate,
} from '@patternfly/react-core'

import { FolderIcon, FolderOpenIcon } from '@patternfly/react-icons'

import {
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  ExpandableRowContent,
  truncate
} from '@patternfly/react-table';

import React, { useRef, useState } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import { getContext } from './Context';

import {CountersButtonModal} from './Counters'

import {SystemOperations} from './SystemOperations'


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-gtmonitor");

const logPrefix             = "** GtMonitor ** ";
  
//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// GtMonitor
// ********************************************************************************************************
// ********************************************************************************************************

const GtMonitor : React.FunctionComponent = () => {

  const [hawtconfig, setHawtconfig] = useState<Hawtconfig | null>(null);

  // ********************************************************************************************************

  const regexName             = /,name=([^,]+)/;
  const regexListener         = /,Listener=([^,]+)/;
  const regexServerPool       = /,ServerPool=([^,]+)/;
  const regexServer           = /,Server=([^,]+)/;
  const regexJettyContext     = /:context=([^,]+)/;
  const regexGtMonType        = /:type=([^,]+)/;
  const regexJavaType         = /:type=([^,]+)/;
  const regexBrokerAddress    = /,address=([^,]+)/;

  // ********************************************************************************************************

  type Component = {
    id                  : string,
    title               : string,
    path                : string,
    ignoreData          : boolean,
    displayDetails      : boolean,
    multiValued         : boolean,
    regexName           : any,
    regexSubname        : any,
    filteredAttributes  : string[],
    statusAttribute     : string | null,
    statusLevels        : ([any, number]|[any, number, any])[],
    headerAttributes    : string[],
    headerNames         : string[],
    headerId            : string | null,
    bodyFromHeaders     : boolean,
    bodyIds             : any,
    retrievedData       : any,
    computedData        : any,
    systemCounters      : boolean | string | null,
    systemOperations    : boolean | string | null,
  };
 
  // ********************************************************************************************************

  const [gtmonitor, setGtMonitor] = useState<{[key : string] : Component}> ({
    // GtMonitor
    'runtime' : {
      id                      : "runtime",
      title                   : "Runtime",
      path                    : "",
      ignoreData              : true,
      displayDetails          : false,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["instanceName", "gtMonitorVersion", "instanceStatus"],
      headerNames             : ["Name", "Version", "Status"],
      headerId                : "instanceManager",
      bodyFromHeaders         : true,
      bodyIds                 : [],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'instanceManager' : {
      id                      : "instanceManager",
      title                   : "Instance Manager",
      path                    : "com.csc.gtmonitor.*:type=InstanceManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "instanceStatus",
      statusLevels            : [["RUNNING", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["remoteAdminServer", "statisticsManager", "statisticsManagerCounters"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : true
    },
    'listenerManager' : {
      id                      : "listenerManager",
      title                   : "Listener Manager",
      path                    : "com.csc.gtmonitor.*:type=ListenerManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : ["name"],
      statusAttribute         : "status",
      statusLevels            : [["ENABLED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["listeners", "listenerConnections", "listenerProxies"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'serverPoolManager' : {
      id                      : "serverPoolManager",
      title                   : "Server Pool Manager",
      path                    : "com.csc.gtmonitor.*:type=ServerPoolManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : ["name"],
      statusAttribute         : "status",
      statusLevels            : [["ENABLED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["serverPools", "serverPoolQueues", "serverPoolServerInstances"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'statisticsManager' : {
      id                      : "statisticsManager",
      title                   : "Statistics Manager",
      path                    : "com.csc.gtmonitor.*:type=StatisticsManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexGtMonType,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["statisticsLevel", "sliceSeconds", "statisticsFlushPeriod"],
      headerNames             : ["Level", "Time slice (s)", "Flush period (min)"],
      headerId                : "statisticsManager",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'statisticsManagerCounters' : {
      id                      : "statisticsManagerCounters",
      title                   : "Statistics Counters",
      path                    : "com.csc.gtmonitor.*:type=StatisticsManager.Counters",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexGtMonType,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["failedCount", "requestCount", "timeoutCount"],
      headerNames             : ["Failed count", "Request count", "Timeout count"],
      headerId                : "statisticsManagerCounters",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'remoteAdminServer' : {
      id                      : "remoteAdminServer",
      title                   : "Remote Admin Server",
      path                    : "com.csc.gtmonitor.*:type=RemoteAdminServer",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexGtMonType,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [["Awaiting control client attach", 1]],
      headerAttributes        : ["port", "activeClientCount", "maxActiveClientCount"],
      headerNames             : ["Port", "Clients", "Max clients"],
      headerId                : "remoteAdminServer",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },

    // Listeners
    'listeners' : {
      id                      : "listeners",
      title                   : "Listeners",
      path                    : "com.csc.gtmonitor.*:type=Listener,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [["Sleeping/Waiting", 1]],
      headerAttributes        : ["failed", "served", "succeeded", "asynchronous", "timedOut"],
      headerNames             : ["Failed", "Served", "Succeeded", "Asynchronous", "Timed out"],
      headerId                : "listenerCounters",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : true
    },
    'listenerCounters' : {
      id                      : "listenerCounters",
      title                   : "Listener Counters",
      path                    : "com.csc.gtmonitor.*:type=Listener.Counters,Listener=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexListener,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'listenerConnections' : {
      id                      : "listenerConnections",
      title                   : "Listener Connections",
      path                    : "com.csc.gtmonitor.*:type=Listener.Connection,Listener=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexListener,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "closed",
      statusLevels            : [[false, 1, "Opened"], [true, 2, "Closed"]],
      headerAttributes        : ["queuedJobCount"],
      headerNames             : ["Queued Jobs"],
      headerId                : "listenerConnections",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : true
    },
    'listenerProxies' : {
      id                      : "listenerProxies",
      title                   : "Listener Proxies",
      path                    : "com.csc.gtmonitor.*:type=Listener.Proxy,Listener=*,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexListener,
      regexSubname            : regexName,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["served", "currentWaitTime", "status"],
      headerNames             : ["Served", "Wait Time (ms)", "Status"],
      headerId                : "listenerProxies",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : true
    },

    // Server Pools
    'serverPools' : {
      id                      : "serverPools",
      title                   : "Server Pools",
      path                    : "com.csc.gtmonitor.*:type=ServerPool,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [["ENABLED", 1]],
      headerAttributes        : ["failed", "served", "succeeded"],
      headerNames             : ["Failed", "Served", "Succeeded"],
      headerId                : "serverPoolCounters",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : true,
      systemOperations        : true
    },
    'serverPoolCounters' : {
      id                      : "serverPoolCounters",
      title                   : "Server Pool Counters",
      path                    : "com.csc.gtmonitor.*:type=ServerPool.Counters,ServerPool=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexServerPool,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'serverPoolQueues' : {
      id                      : "serverPoolQueues",
      title                   : "Server Pool Queues",
      path                    : "com.csc.gtmonitor.*:type=ServerPool.Queue,ServerPool=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexServerPool,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["items", "waiters"],
      headerNames             : ["Queued items", "Waiting callers"],
      headerId                : "serverPoolQueues",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : true
    },
    'serverPoolServers' : {
      id                      : "serverPoolServers",
      title                   : "Server Pool Server",
      path                    : "com.csc.gtmonitor.*:type=ServerPool.Server,ServerPool=*,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexServerPool,
      regexSubname            : regexName,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'serverPoolServerInstances' : {
      id                      : "serverPoolServerInstances",
      title                   : "Server Pool Server Instances",
      path                    : "com.csc.gtmonitor.*:type=ServerPool.Server.Instance,ServerPool=*,Server=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexServerPool,
      regexSubname            : regexServer,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["serverId", "requests", "restarts", "currentWaitTime", "timeIdle"],
      headerNames             : ["Server Id", "Requests", "Restarts", "Wait Time (ms)", "Idle Time (ms)"],
      headerId                : "serverPoolServers",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : true,
      systemOperations        : true
    },

    // Java
    'java' : {
      id                      : "java",
      title                   : "Java",
      path                    : "",
      ignoreData              : true,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : "java",
      bodyFromHeaders         : false,
      bodyIds                 : ["javaRuntime", "javaOS", "javaThreading"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'javaRuntime' : {
      id                      : "javaRuntime",
      title                   : "Java Runtime",
      path                    : "java.lang:type=Runtime",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexJavaType,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : "javaRuntime",
      bodyFromHeaders         : false,
      bodyIds                 : [],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'javaOS' : {
      id                      : "javaOS",
      title                   : "Operating System",
      path                    : "java.lang:type=OperatingSystem",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexJavaType,
      regexSubname            : null,
      filteredAttributes      : ["ObjectName"],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["AvailableProcessors", "FreeMemorySize", "TotalMemorySize"],
      headerNames             : ["Processors", "Free Memory (bytes)", "Total Memory (bytes)"],
      headerId                : "javaOS",
      bodyFromHeaders         : false,
      bodyIds                 : [],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'javaThreading' : {
      id                      : "javaThreading",
      title                   : "Threading",
      path                    : "java.lang:type=Threading",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexJavaType,
      regexSubname            : null,
      filteredAttributes      : ["AllThreadIds", "ObjectName"],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["ThreadCount", "PeakThreadCount", "DaemonThreadCount", "TotalStartedThreadCount"],
      headerNames             : ["Threads", "Peak Threads", "Daemon Threads", "Total Started Threads"],
      headerId                : "javaThreading",
      bodyFromHeaders         : false,
      bodyIds                 : [],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },

    // Jetty
    'jetty' : {
      id                      : "jetty",
      title                   : "Jetty",
      path                    : "com.csc.gtmonitor.jetty:context=*,type=server,id=0",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "state",
      statusLevels            : [["STARTED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["jettyContextHandlers", "jettyWebappContexts"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'jettyContextHandlers' : {
      id                      : "jettyContextHandlers",
      title                   : "Context Handlers",
      path                    : "com.csc.gtmonitor.jetty:context=*,type=contexthandler,id=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexJettyContext,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "state",
      statusLevels            : [["STARTED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'jettyWebappContexts' : {
      id                      : "jettyWebappContexts",
      title                   : "Webapp Contexts",
      path                    : "com.csc.gtmonitor.jetty:context=*,type=webappcontext,id=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexJettyContext,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "state",
      statusLevels            : [["STARTED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
  
    // Camel
    'camelManager' : {
      id                      : "camelManager",
      title                   : "Camel Manager",
      path                    : "com.csc.gtmonitor.*:type=CamelManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [["Started", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["camelContext", "camelComponents", "camelRoutes", "camelEndpoints", "camelConsumers", "camelProducers", "camelProcessors"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelContext' : {
      id                      : "camelContext",
      title                   : "Context",
      path                    : "org.apache.camel:context=*,type=context,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      statusAttribute         : "State",
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusLevels            : [["Started", 1]],
      headerAttributes        : ["StartedRoutes", "TotalRoutes", "ExchangesFailed", "ExchangesCompleted", "ExchangesInflight", "ExchangesTotal"],
      headerNames             : ["Started Routes", "Total Routes", "Exchanges Failed", "Exchanges Completed", "Exchanges Inflight", "Exchanges Total"],
      headerId                : "camelContext",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelComponents' : {
      id                      : "camelComponents",
      title                   : "Components",
      path                    : "org.apache.camel:context=*,type=components,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelRoutes' : {
      id                      : "camelRoutes",
      title                   : "Routes",
      path                    : "org.apache.camel:context=*,type=routes,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : ["ExchangesFailed", "ExchangesCompleted", "ExchangesInflight", "ExchangesTotal"],
      headerNames             : ["Exchanges Failed", "Exchanges Completed", "Exchanges Inflight", "Exchanges Total"],
      headerId                : "camelRoutes",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelEndpoints' : {
      id                      : "camelEndpoints",
      title                   : "Endpoints",
      path                    : "org.apache.camel:context=*,type=endpoints,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelConsumers' : {
      id                      : "camelConsumers",
      title                   : "Consumers",
      path                    : "org.apache.camel:context=*,type=consumers,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelProducers' : {
      id                      : "camelProducers",
      title                   : "Producers",
      path                    : "org.apache.camel:context=*,type=producers,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'camelProcessors' : {
      id                      : "camelProcessors",
      title                   : "Processors",
      path                    : "org.apache.camel:context=*,type=processors,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "State",
      statusLevels            : [["Started", 1]],
      headerAttributes        : ["ExchangesFailed", "ExchangesCompleted", "ExchangesInflight", "ExchangesTotal"],
      headerNames             : ["Exchanges Failed", "Exchanges Completed", "Exchanges Inflight", "Exchanges Total"],
      headerId                : "camelProcessors",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },

    // Broker
    'brokerManager' : {
      id                      : "brokerManager",
      title                   : "Broker Manager",
      path                    : "com.csc.gtmonitor.*:type=BrokerManager",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : "status",
      statusLevels            : [["ENABLED", 1]],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : ["broker", "brokerAcceptors", "brokerAddresses"],
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'broker' : {
      id                      : "broker",
      title                   : "Broker",
      path                    : "org.apache.activemq.artemis:broker=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : false,
      regexName               : null,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["TotalConnectionCount", "TotalConsumerCount", "TotalMessageCount"],
      headerNames             : ["Total Connections", "Total Consumers", "Total Messages"],
      headerId                : "broker",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'brokerAcceptors' : {
      id                      : "brokerAcceptors",
      title                   : "Acceptors",
      path                    : "org.apache.activemq.artemis:broker=*,component=acceptors,name=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexName,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : [],
      headerNames             : [],
      headerId                : null,
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
    'brokerAddresses' : {
      id                      : "brokerAddresses",
      title                   : "Addresses",
      path                    : "org.apache.activemq.artemis:broker=*,component=addresses,address=*",
      ignoreData              : false,
      displayDetails          : true,
      multiValued             : true,
      regexName               : regexBrokerAddress,
      regexSubname            : null,
      filteredAttributes      : [],
      statusAttribute         : null,
      statusLevels            : [],
      headerAttributes        : ["MessageCount", "RoutedMessageCount", "UnRoutedMessageCount"],
      headerNames             : ["Messages", "Routed Messages", "UnRouted Messages"],
      headerId                : "brokerAddresses",
      bodyFromHeaders         : false,
      bodyIds                 : null,
      retrievedData           : false,
      computedData            : [],
      systemCounters          : null,
      systemOperations        : null
    },
  })

  // ********************************************************************************************************

  type DetailData = {
    name          : string,
    value         : any,
    description   : string
  };

  type SimpleData = DetailData;

  type MultiValuedData = {
    id            : string,
    name          : string;
    subname       : string;
    status        : any;
    headers       : any[],
    details       : DetailData[];
  }

  type ComponentData = SimpleData | MultiValuedData;

  // ********************************************************************************************************

  const Components : string[] = [
    'runtime',
    'java',
    'jetty',
    'instanceManager',
    'listenerManager',
    'serverPoolManager',
    'camelManager',
    'brokerManager'
  ] 

  // ********************************************************************************************************

  const defaultComponentWithBodyExpanded      = true;
  const defaultComponentWithoutBodyExpanded   = false;
  const defaultComponentDetailsExpanded       = false;
  const defaultComponentBodyExpanded          = true;
  const defaultComponentSubBodyExpanded       = true;
  const defaultComponentSubBodyItemExpanded   = false;
  const defaultComponentBodyItemExpanded      = false;

  let headerSizes: number[] = Object.entries(gtmonitor).map((entry) => entry[1].headerAttributes.length);
  let maxHeaders = Math.max(5, ...headerSizes);

  // ********************************************************************************************************

  const expandedElements      = useRef<Map<string, boolean>>(new Map());
  const setElementExpanded    = ((element : string, expanded : boolean) : void => { expandedElements.current.set(element, expanded); })
  const getElementExpanded    = ((element : string) : undefined | boolean => { return expandedElements.current.get(element) });
  const isElementExpanded     = ((element : string) : boolean => { return getElementExpanded(element) === true } );
  const toggleElementExpanded = ((element : string) : void => {
    const expanded = getElementExpanded(element);
    if (expanded !== undefined)
      setElementExpanded(element, !expanded);
  });

  
  // ********************************************************************************************************
  // ********************************************************************************************************
  // User
  // ********************************************************************************************************
  // ********************************************************************************************************

  function getUser() {
    userService.getUsername()
      .then ( (username : any) => {
        log.debug(logPrefix, "getUser: ", username);
      })
      .catch ( (error : any) => {
        log.error(logPrefix, "getUser ERROR: ", error);
      });

  }

  function checkUser() {
    userService.getUsername()
      .then ( (username : any) => {
        log.debug(logPrefix, "checkUser, getUsername: ", username);
      })
      .catch ( (error : any) => {
        log.error(logPrefix, "checkUser, getUsername ERROR: ", error);
      });

    userService.isLogin()
      .then ( (login : any) => {
        log.debug(logPrefix, "checkUser, isLogin: ", login);
      })
      .catch ( (error : any) => {
        log.error(logPrefix, "checkUser, isLogin ERROR: ", error);
      });
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia : IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************
   
  function extractName (input : string, regex : any) : string {
    if (input !== null && regex !== null) {
      const match = input.match(regex);
      return match ? match[1].replaceAll("\"", "") : " ";
    }
    return " ";
  }
 
  // ********************************************************************************************************
 
  async function retrieveDescription(mbean : string) : Promise<any> {
    let description : any = null;

    if (jolokia !== null)
      try { description = await jolokia.list(mbean.replace(":", "/")) } catch (e) {};

    if (description === null)
      log.debug(logPrefix, "retrieveDescription, cannot retrieve description for: ", mbean);
    else
      log.debug(logPrefix, "retrieveDescription, description retrieved for: ", mbean, " is: ", description);

     return description;
  }

  // ********************************************************************************************************
  
  async function retrieveAttributeUsingDescription(mbean : string, attributeName : any) : Promise<any> {
    let attributeValue : any = "Unavailable";
    if (jolokia !== null)
      try {
        attributeValue = await jolokia.getAttribute(mbean, attributeName);
      }
      catch (e) {
      };
    return attributeValue;
  }

  // ********************************************************************************************************

  async function retrieveAttributesUsingDescription(mbean : string, description : any) : Promise<any> {
    const attributes : any = {};
    try {
      if (description == null)
        description = await retrieveDescription(mbean);
      const attributesDescription = (description === null) ? null : Object.entries(description.attr);   // .attr may be unavailable ...
      if (attributesDescription !== null) {
        for (const attributeDescription of attributesDescription) {
          try {
            const attributeName = attributeDescription[0];
            attributes[attributeName] = await retrieveAttributeUsingDescription(mbean, attributeName);
          }
          catch (e) {}
        }
      }
    }
    catch (e) {}
    if (Object.entries(attributes).length === 0) {
      log.debug(logPrefix, "retrieveAttributesUsingDescription, cannot retrieve mbean attributes using description for: ", mbean);
      return attributes;
    }
    else
      log.debug(logPrefix, "retrieveAttributesUsingDescription, mbean attributes retrieved using description for: ", mbean);
    return attributes;
  }

  // ********************************************************************************************************
  
  async function retrieveAttributes(mbean : string, description : any) : Promise<any> {
    //let attributes :  {[key : string] : any} | null  = null;
    let attributes  : ReadResponseValue | any[] = null;
    // try to first bulk retrieve the attributes ...
    log.debug(logPrefix, "retrieveAttributes, calling getAttribute ...", mbean);
    if (jolokia !== null)
      try {
        attributes = await jolokia.getAttribute(mbean)
      } catch (e) {
        // an 500 error may be raised due to unaccessible attributes, when so, attributes must be retrieved one by one ...
        attributes = await retrieveAttributesUsingDescription(mbean, description);
      };
    if (attributes === null) {
      log.debug(logPrefix, "retrieveAttributes, cannot retrieve mbean attributes for: ", mbean);
      attributes = [];
    }
    else
      log.debug(logPrefix, "retrieveAttributes, mbean attributes retrieved for: ", mbean, " are: ", attributes);
      return attributes;
  }

  // ********************************************************************************************************

  async function retrieveMbean(path : string): Promise<any> {
    if (jolokia !== null) {
      const response = await jolokia.search(path);
      if (response != null && response.length > 0) {
        const mbean       = response[0];
        const description = await retrieveDescription(mbean);
        const attributes  = await retrieveAttributes(mbean, description);
        const data        = {
          mbean       : mbean,
          description : description,
          attributes  : attributes
        }
        log.debug(logPrefix, "retrieveMbean, mbean retrieved: ", mbean);

        return data;
      }
    }
    else
      log.error(logPrefix, "retrieveMbean, jolokia undefined when retrieving mbean from path: ", path);

      return null;
  }

  // ********************************************************************************************************

  async function retrieveMultiMbean(path : string, regexName : any, regexSubname : any) : Promise<any> {
    if (jolokia !== null) {
      const response : any    = await jolokia.search(path);
      const data     : any[]  = [];

      if (response != null) {
        for (const mbean of response) {
          const description = await retrieveDescription(mbean);
          const attributes  = await retrieveAttributes(mbean, description);

          if (attributes != null) {
            const object = {
              mbean       : mbean,
              name        : extractName(mbean, regexName),
              subname     : extractName(mbean, regexSubname),
              description : description,
              attributes  : attributes
            }
            data.push(object);
            log.debug(logPrefix, "retrieveMultiMbean, multi mbean retrieved: ", mbean);
          }
          else
            log.debug(logPrefix, "retrieveMultiMbean, cannot retrieve multi mbean: ", mbean);
        }
      }
      log.debug(logPrefix, "retrieveMultiMbean, multi mbean retrieved: " + path + ", data: ", data);

      return data;
    }
    else
      log.error(logPrefix, "retrieveMultiMbean, jolokia undefined when retrieving ", path);

      return null;
  }

  // ********************************************************************************************************

  async function retrieveData(component : Component) : Promise<any> {
    log.debug(logPrefix, "********************** retrieveData START for: ", component.title);
    component.retrievedData = false;
    if (component !== undefined) {
      if (component.retrievedData === false)
        if (component.multiValued)
          if (component.ignoreData) {
            log.debug(logPrefix, "retrieveData, ignoring multi valued data for: ", component.title);
            component.retrievedData = [];
          }
          else
            component.retrievedData = await retrieveMultiMbean(component.path, component.regexName, component.regexSubname);
        else
          if (component.ignoreData) {
            log.debug(logPrefix, "retrieveData, ignoring simple data for: ", component.title);
            component.retrievedData = {
              mbean       : component.path,
              description : {},
              attributes  : {}
            };
          }
          else
            component.retrievedData = await retrieveMbean(component.path);
      if (component.retrievedData == null)
        log.warn(logPrefix, "retrieveData, cannot retrieve data for: ", component.title);
    }
    else
      log.warn(logPrefix, "retrieveData, cannot retrieve undefined component");
    log.debug(logPrefix, "********************** retrieveData END for: ", component.title);
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  function computeDetailsData(description : any, attributes : any, filtered : any) : DetailData[] {
    let data : DetailData[] | null = null;

    try {
      let objectData : [string, any][] = Object.entries(attributes);

      objectData = (filtered == null) ? objectData : objectData.filter((attribute : [string, any]) => {
        return (filtered.indexOf(attribute[0]) < 0);
      });
      
      objectData = objectData.sort((attributeA : [string, any], attributeB : [string, any]) => {
        const nameA = attributeA[0].toLowerCase();
        const nameB = attributeB[0].toLowerCase();
        if (nameA > nameB) return  1;
        if (nameA < nameB) return -1;
        return 0;
      });

      data = objectData.map((attribute : [string, any]) => {
          const attributeName        = attribute[0];
          let   attributeValue       = attribute[1];
          let   attributeDescription = "";
          if ((attributeValue !== null) && (typeof attributeValue === "object") && (attributeValue.hasOwnProperty('objectName')))
            attributeValue = attribute[1].objectName;
          if (description !== null && attributeName !== null)
            try { attributeDescription = description.attr[attributeName].desc } catch (e) {}
          return {
            name        : attributeName,
            value       : attributeValue,
            description : attributeDescription
          };
        })
      }
      catch (e) {
        log.debug(logPrefix, "computeDetailsData, exception while computing details data: ", e);
      }

      return (data === null ? [] : data);
  }

  // ********************************************************************************************************

  function computeSimpleData(component : Component) : any {
    let data : SimpleData[] = [];

    try {
      data = computeDetailsData(component.retrievedData.description, component.retrievedData.attributes, component.filteredAttributes);
      log.debug(logPrefix, "computeSimpleData, computed simple data for: ", component.id);
    }
    catch (e) {
      log.debug(logPrefix, "computeSimpleData, exception while computing simple data for: ", component.id, ", exception is: ", e);
    }

    return data;
  }

  // ********************************************************************************************************
 
  function computeMultiValuedItemData(gtmon : any, component : Component, componentItem : any) : MultiValuedData | null {
    if (component === null)
      return null;

//  log.debug(logPrefix, "computeMultiValuedItemData, computing multi valued item data for: ", component.id);

    try {
      const counterComponentId = component.headerId;
      let headers = null;
      if (counterComponentId !== null) {
        const counterComponent = gtmon[counterComponentId];
        headers = counterComponent.retrievedData.filter(
          (counter : any) => (
            ((counterComponent.regexSubname === null) ? counter.name : counter.subname) === ((component.regexSubname === null) ? componentItem.name : componentItem.subname)
          )
        );
      }

      const headersItem = (headers != null && headers.length > 0) ? headers[0] : null;

      const itemData : MultiValuedData = {
        id        : component.id,
        name      : componentItem.name,
        subname   : componentItem.subname,
        status    : (component.statusAttribute === null) ? null : componentItem.attributes[component.statusAttribute],
        headers   : component.headerAttributes.map((headerAttribute : string) => (headersItem === null || headersItem.attributes[headerAttribute] === null) ? null : headersItem.attributes[headerAttribute]),
        details   : computeDetailsData(componentItem.description, componentItem.attributes, component.filteredAttributes)
      }

      if (itemData.id === null || itemData === undefined)
        log.debug(logPrefix, "computeMultiValuedItemData, problem with id of computed multi valued data header value for: ", component.id);

//    itemData.headers.forEach((value) => {
//      log.debug(logPrefix, "computeMultiValuedItemData, computed multi valued data header value for: ", component.id, ", value is: ", value);
//    })

      return itemData;
    }
    catch (e) {
      log.debug(logPrefix, "computeMultiValuedItemData, exception while computing multi valued item data for: ", component.id, ", exception is : ", e);
      return null;
    }
  }

  // ********************************************************************************************************

  function computeMultiValuedData(gtmon : any, component : Component) : any {
    let data  : MultiValuedData[] = [];

    try {
      if (component.retrievedData !== false && component.retrievedData.length > 0) {
        component.retrievedData.forEach((componentItem : any) => {
          log.debug(logPrefix, "computeMultiValuedData, computing multi valued data for: ", component.id, " / ", componentItem.name);
          const itemData = computeMultiValuedItemData(gtmon, component, componentItem);
          if (itemData !== null)
            data.push(itemData);
        })
      }

      data = data.sort((dataA : MultiValuedData, dataB : MultiValuedData) => {
  //    const nameA = dataA.name.toLowerCase();
  //    const nameB = dataB.name.toLowerCase();
        const nameA = dataA.name;
        const nameB = dataB.name;
        if (nameA > nameB) return  1;
        if (nameA < nameB) return -1;

  //    const subnameA = dataA.subname.toLowerCase();
  //    const subnameB = dataB.subname.toLowerCase();
        const subnameA = dataA.subname;
        const subnameB = dataB.subname;
        if (subnameA > subnameB) return  1;
        if (subnameA < subnameB) return -1;

        return 0;
      })

      log.debug(logPrefix, "computeMultiValuedData, computed multi valued data for: ", component.id);
    }
    catch (e) {
      log.debug(logPrefix, "computeMultiValuedData, exception while computing multi valued data for: ", component.id, ", exception is : ", e);
    }

    return data;
  }

  // ********************************************************************************************************
  
  async function computeData(gtmon : any , component : Component) {
    log.debug(logPrefix, "********************** computeData START for: ", component.title);
    const data : ComponentData[] = (component.ignoreData) ? [] : (
      (component.multiValued) ? computeMultiValuedData(gtmon, component)
                                                           : computeSimpleData(component));
    component.computedData = data;
    log.debug(logPrefix, "********************** computeData END for: ", component.title, data);
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Component rendering
  // ********************************************************************************************************
  // ********************************************************************************************************

  const valueToDisplay = (value : any) : string => {
    let   display : any = null;

    const tvalue = typeof value;

    switch (tvalue) {
      case 'undefined'  : display = 'undefined';
                          break;
      case 'string'     :
      case 'number'     :
      case 'bigint'     : display = "" + value;
                          break;
      case 'boolean'    : display = value ? "true" : "false";
                          break;
      case 'symbol'     : display = value.toString();
                          break;
      case 'function'   : display = 'function: ' + value.toString();
                          break;
      case 'object'     : if (value === null)
                            display = 'null'
                          else
                          if (value.hasOwnProperty('objectName')) 
                            display = value.objectName;
                          else
                          if (Array.isArray(value) && value.length === 0)
                            display = '[]'
                          else
                            display = value.toString();
                          break;
      default           : display = value.toString();
    }

    return display;
  }

  // ********************************************************************************************************
  
  const SystemCounters = (props : {component : any, item : any}) : any => {
    const systemCounters = props.component.systemCounters;
    const item = props.item;

    if (!context.canAccessCounters)
      return <></>

    if (!systemCounters)
      return <></>

    let serverPoolName = null;
    let serverName     = null;

    if (typeof item === "string") {
      serverPoolName = item.trim();
    }
    else {
      serverPoolName = (item.name === null || item.name === undefined) ? null : item.name.trim();
      serverName     = (item.subname === null || item.subname === undefined) ? null : item.subname.trim();
    }

    serverPoolName  = (serverPoolName === "") ? null : serverPoolName;
    serverName      = (serverName === "") ? null : serverName;

    if (serverPoolName !== null && serverName !== null)
      return <CountersButtonModal serverPoolName = {serverPoolName} serverName = {serverName}/>
    if (serverPoolName !== null)
      return <CountersButtonModal serverPoolName = {serverPoolName}/>
    if (serverName !== null)
      return <CountersButtonModal serverName = {serverName}/>

    return <CountersButtonModal/>
  }

  // ********************************************************************************************************
  
  const DetailName = (props : any) : any => {
    const name : any = props.name;

    return (
      <TextContent tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px'}}>
        <Text component = {TextVariants.h5} tabIndex = {-1} align = 'left' style = {{marginTop : '0px', marginBottom : '0px'}}>
          <Truncate tabIndex = {-1} className = "dxc-c-detail__name" content = {name} style = {{marginTop : '0px', marginBottom : '0px'}}/>
        </Text>
      </TextContent>
    )
  }

  // ********************************************************************************************************

  const DetailDescription = (props : any) : any => {
    const description : any = props.description;

    return (
      <TextContent tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px'}}>
        <Text component = {TextVariants.h5} tabIndex = {-1} align = 'left' style = {{marginTop : '0px', marginBottom : '0px'}}>
          <Truncate tabIndex = {-1} className = "dxc-c-detail__description" content = {description} style = {{marginTop : '0px', marginBottom : '0px'}}/>
        </Text>
      </TextContent>
    )
  }

  // ********************************************************************************************************

  const SimpleDetailValue = (props : any) : any => {
    const value   : any = props.value;

    const display : any = valueToDisplay(value);

    return (
      <Text component = {TextVariants.h5} tabIndex = {-1} align = 'left' style = {{marginTop : '0px', marginBottom : '0px'}}>
        <Truncate tabIndex = {-1} className = "dxc-c-detail__value" content = {display} style = {{marginTop : '0px', marginBottom : '0px'}}/>
      </Text>
    )
  }

  // ********************************************************************************************************

  const DetailValue = (props : any) : any => {
    const value : any = props.value;

    if (Array.isArray(value))
      return (
        <TextContent tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px'}}>
        {
          value.map((item : any, itemIndex : any) => <SimpleDetailValue tabIndex = {-1} key = {itemIndex} value = {item}/>)
        }
        </TextContent>
      )
    else
      return (
        <TextContent tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px'}}>
          <SimpleDetailValue tabIndex = {-1} value = {value}/>
        </TextContent>
      )
  }
  
  // ********************************************************************************************************

  const Detail = (props : any) : any => {
    const detail : DetailData = props.detail;

    if (detail === null)
      return (<>{' '}</>);

    return (
      <GridItem tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px', paddingLeft : '0px', paddingRight : '0px', paddingTop : '0px', paddingBottom : '0px'}}>
        <Grid tabIndex = {-1} style = {{marginTop : '0px', marginBottom : '0px', paddingLeft : '0px', paddingRight : '0px', paddingTop : '0px', paddingBottom : '0px'}}>
          <GridItem tabIndex = {-1} span = {2}><DetailName        name        = {detail.name}/></GridItem>
          <GridItem tabIndex = {-1} span = {2}><DetailDescription description = {detail.description}/></GridItem>
          <GridItem tabIndex = {-1} span = {1}>{' '}</GridItem>
          <GridItem tabIndex = {-1} span = {7}><DetailValue       value       = {detail.value}/></GridItem>
       </Grid>
      </GridItem>
    )
  }

  // ********************************************************************************************************

  const Details = (props : any) : any => {
    let details     : DetailData[]  = props.details;
    let isExpanded  : any           = props.isExpanded;
    let isHidden    : any           = props.isHidden;

    const detailsExpanded = (isExpanded === undefined) ?  true : isExpanded;
    const detailsHidden   = (isHidden   === undefined) ? false : isHidden;

    if (details === null || details.length === 0)
      return (
        <Tr tabIndex = {-1} isStriped isHidden = {true} isExpanded = {false}>
          <Td tabIndex = {-1}>{' '}</Td>
        </Tr>
      );

    // kind of magic computing for adding or not a vertical scroller ...
    const maxDetails = 20;
    const nbDetails = details
      .map((detail : DetailData) => ((Array.isArray(detail.value) && detail.value.length > 0) ? detail.value.length : 1))
      .reduce((sum, value) => sum + value, 0);
    const overflowY : any | undefined = (nbDetails <= maxDetails) ? 'none' : 'auto';
    const maxHeight = (nbDetails <= maxDetails) ? 'auto' : (Math.round(maxDetails * 1.8) + 2).toString() + 'em';

    return (
      <Tr tabIndex = {-1} isStriped isHidden = {detailsHidden} isExpanded = {detailsExpanded} style = {{paddingLeft : '0px', paddingRight : '0px', paddingTop : '0px', paddingBottom : '0px'}}>
        <Td tabIndex = {-1} hasLeftBorder colSpan = {2 + maxHeaders} style = {{paddingLeft : '0px', paddingRight : '0px', paddingTop : '0px', paddingBottom : '0px'}}>
          <ExpandableRowContent style = {{paddingLeft : '0px', paddingRight : '0px', paddingTop : '0px', paddingBottom : '0px'}}>
            <Grid tabIndex = {0} hasGutter = {false} style = {{paddingLeft : '20px', paddingRight : '20x', paddingTop : '8px', paddingBottom : '8px', maxHeight : maxHeight, overflowY : overflowY}}>
            {
              details.map((detail : DetailData, detailIndex : any) => (<Detail tabIndex = {-1} key = {detailIndex} detail = {detail}/>))
            }
            </Grid>
          </ExpandableRowContent>
        </Td>
      </Tr>
    )
  }

  // ********************************************************************************************************

  const ComponentDetails = (props : any) => {
    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
    const bodyIds       : string[]          = component.bodyIds;
    const details       : DetailData[]      = component.computedData;

    if (component.ignoreData || !component.displayDetails)
      return <></>

    const hasBodyIds    : boolean           = (bodyIds !== null && bodyIds.length > 0);

    const element       : string            = "ComponentDetails_" + id;

    const [detailsRefresh, setDetailsRefresh] = useState(false);

    if (getElementExpanded(element) === undefined) {
      const expanded = hasBodyIds ? defaultComponentDetailsExpanded : true;
      setElementExpanded(element, expanded);
    };

    const handleDetailsClick = () => {
      if (window.getSelection()?.toString()) return;
      toggleElementExpanded(element);
      setDetailsRefresh(!detailsRefresh);
    }

    const handleDetailsKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleElementExpanded(element);
        setDetailsRefresh(!detailsRefresh);
      }
    }

    const isDetailsExpanded = () => { return isElementExpanded(element); }

    if (!hasBodyIds)
      return (
        <Card tabIndex = {-1} key = {id + "Details"} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {isDetailsExpanded()} >
          <CardExpandableContent tabIndex = {-1}>
            <CardBody tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
              <Table tabIndex = {-1} variant= 'compact'>
                <Tbody tabIndex = {-1}>
                  <Details tabIndex = {-1} isExpanded = {true} details = {details} />
                </Tbody>
              </Table>
            </CardBody>
          </CardExpandableContent>
        </Card>
      )
    else {
      return (
        <Card tabIndex = {-1} key = {id + "Details"} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {isDetailsExpanded()} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
          <CardHeader tabIndex = {0} style = {{cursor : 'pointer', paddingTop : "4px", paddingBottom : "4px"}} isToggleRightAligned = {true} onKeyDown = {handleDetailsKeyDown} onClick = {() => handleDetailsClick()}>
            <CardTitle tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
              <TextContent tabIndex = {-1}>
                <Text tabIndex = {-1} align = 'left' component = {TextVariants.h4}>Details</Text>
              </TextContent>
            </CardTitle>
          </CardHeader>
          <CardExpandableContent tabIndex = {-1}>
            <CardBody tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
              <Table tabIndex = {-1} variant= 'compact'>
                <Tbody tabIndex = {-1}>
                  <Details tabIndex = {-1} isExpanded = {true} details = {details} />
                </Tbody>
              </Table>
            </CardBody>
          </CardExpandableContent>
        </Card>
      )
    }
  }

  // ********************************************************************************************************

  const ComponentStatus = (props : any) => {
    const id              : string          = props.id;
    const status          : any             = props.status;

    if (status === null || status === undefined)
      return <>{" "}</>

    const component       : Component           = gtmonitor[id];

    const statusLevels    : ([any, number]|[any, number, any])[] = component.statusLevels;

    const statusLevelItem : [any, number] | [any, number, any] | null | undefined = statusLevels.find((item : any[]) => (item [0] === status));

    const statusLevel     : number = (statusLevelItem === null || statusLevelItem === undefined) ? 0 : statusLevelItem[1];

    const statusText      : string = (statusLevelItem !== null && statusLevelItem !== undefined && statusLevelItem.length === 3 && statusLevelItem[2] !== null) ? valueToDisplay(statusLevelItem[2]) : valueToDisplay(status);

    let   statusColor     : any = "grey";
    switch (statusLevel) {
      case 0 : statusColor = "grey";   break;  // undefined
      case 1 : statusColor = "green";  break;  // ok
      case 2 : statusColor = "orange"; break;  // warn
      case 3 : statusColor = "red";    break;  // error
    }

    return (
      <Label tabIndex = {-1} className = "dxc-c-label" color = {statusColor}>{statusText}</Label>      
    )
  }

  // ********************************************************************************************************

  const HeaderNames = (props : any) : any => {
    log.debug(logPrefix, "HeaderNames, id: ", props.id);

    const id                  : string          = props.id;
    const component           : Component       = gtmonitor[id];
    const headerNames         : string[]        = component.headerNames;

    return (
      <>
        {
          headerNames.map((headerName : string, headerNameIndex : any) => (
            <Th tabIndex = {-1} key = {headerNameIndex} aria-label = { "headername-" + id + "-" + headerName } width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{headerName}</Th>
          ))
        }
        <HeaderNamesPadding tabIndex = {-1} id = {id}/>
      </>
    )
  }

  // ********************************************************************************************************
 
  const HeaderNamesPadding = (props : any) : any => {
    log.debug(logPrefix, "HeaderNamesPadding, id: ", props.id);

    const id                  : string          = props.id;
    const component           : Component       = gtmonitor[id];
    const headerNames         : string[]        = component.headerNames;
    let   headerNamesPadding  : any[]           = [];
    
    for (let emptyHeaders = headerNames.length; emptyHeaders < maxHeaders; emptyHeaders++) {
      headerNamesPadding.push(<Th tabIndex = {-1} key = {emptyHeaders} aria-label = { "headernamepadding-" + id + "-" + emptyHeaders } width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{" "}</Th>)
    }

    return headerNamesPadding;
  }

  // ********************************************************************************************************

  const HeaderValues = (props : any) : any => {
    log.debug(logPrefix, "HeaderValues, id: ", props.id);

    const id                  : string          = props.id;
    const data                : MultiValuedData = props.data;

    return (
      <>
        {
          data.headers.map((headerValue : any, headerIndex : any) => (
            <Td tabIndex = {-1} key = {headerIndex} width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{(headerValue === null) ? " " : valueToDisplay(headerValue)}</Td>
          ))
        }
        <HeaderValuesPadding tabIndex = {-1} key = {id} id = {id}/>
      </>
    )
  }

  // ********************************************************************************************************
 
  const HeaderValuesPadding = (props : any) : any => {
    log.debug(logPrefix, "HeaderValuesPadding, id: ", props.id);

    const id                  : string          = props.id;
    const component           : Component       = gtmonitor[id];
    const headerNames         : string[]        = component.headerNames;
    let   headerValuesPadding : any[]           = [];

    for (let emptyHeaders = headerNames.length; emptyHeaders < maxHeaders; emptyHeaders++) {
      headerValuesPadding.push(<Td tabIndex = {-1} key = {emptyHeaders} width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{" "}</Td>)
    }

    return headerValuesPadding;
  }

  // ********************************************************************************************************

  const EmpytHeaderValues = (props : any) : any => {
    let empytHeaderValues : any[] = [];

    for (let emptyHeaders = 0; emptyHeaders < maxHeaders; emptyHeaders++) {
      empytHeaderValues.push(<Td tabIndex = {-1} key = {emptyHeaders} width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{" "}</Td>)
    }

    return empytHeaderValues
  }

  // ********************************************************************************************************

  const SubBody = (props : any) => {
    log.debug(logPrefix, "SubBody, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
    const data          : MultiValuedData[] = component.computedData;
    const subBody       : string            = props.subBody;

    const [subBodyRefresh, setSubBodyRefresh] = useState(false);

    const elementSubBody : string = "SubBody_" + id + "_" + subBody;

    if (getElementExpanded(elementSubBody) === undefined) {
      const expanded = defaultComponentSubBodyExpanded;
      setElementExpanded(elementSubBody, expanded);
    };

    const handleSubBodyClick = () => {
      if (window.getSelection()?.toString()) return;
      toggleElementExpanded(elementSubBody);
      setSubBodyRefresh(!subBodyRefresh);
    }

    const handleSubBodyKeyDown = (e: React.KeyboardEvent<HTMLTableRowElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleElementExpanded(elementSubBody);
        setSubBodyRefresh(!subBodyRefresh);
      }
    }

    const isSubBodyExpanded = () => { return isElementExpanded(elementSubBody); }

    const elementBodyItem : string = "BodyItem_" + id + "_" + subBody;

    if (getElementExpanded(elementBodyItem) === undefined) {
      const expanded = defaultComponentSubBodyItemExpanded;
      setElementExpanded(elementBodyItem, expanded);
    };

    const isBodyItemExpanded = () => { return isElementExpanded(elementBodyItem); }

    // we must save the original body item index ...
    const bodyItemsWithIndex : {bodyItem : MultiValuedData, bodyItemIndex : number}[] = data
      .map((bodyItem, bodyItemIndex) => ({bodyItem : bodyItem, bodyItemIndex : bodyItemIndex}))
      .filter((bodyItemWithIndex, index, array) => (bodyItemWithIndex.bodyItem.name === subBody));

    const nbBodyItems        : number  = bodyItemsWithIndex.length;
    const color              : string  = (nbBodyItems > 0) ? "green" : "orange";
    const status             : any     = <Label tabIndex = {-1} color = {color}>
                                           <TextContent tabIndex = {-1}>
                                             <Text tabIndex = {-1} className = "dxc-c-label" color = {color}>{nbBodyItems}</Text>
                                           </TextContent>
                                         </Label>

    const SubBodyItem = (props : any) => {
      log.debug(logPrefix, "SubBodyItem, subBody: ", props.subBody);

      const id            : string          = props.id;
      const bodyItem      : MultiValuedData = props.bodyItem;

      const [subBodyItemRefresh, setSubBodyItemRefresh] = useState(false);

      const cursor = (bodyItem.details === null || bodyItem.details.length === 0) ? 'context-menu' : 'pointer';

      const handleBodyItemClick = () => {
        if (window.getSelection()?.toString()) return;
        toggleElementExpanded(elementBodyItem);
        setSubBodyItemRefresh(!subBodyItemRefresh);
      }
  
      const handleBodyItemKeyDown = (e: React.KeyboardEvent<HTMLTableRowElement>) => {
        if (e.key === KeyTypes.Enter) {
          toggleElementExpanded(elementBodyItem);
          setSubBodyItemRefresh(!subBodyItemRefresh);
        }
      }
      
      return (
        <Tbody>
          <Tr tabIndex = {0} isHidden = {!isSubBodyExpanded()} style = {{cursor : cursor}} onKeyDown = {handleBodyItemKeyDown} onClick = {() => handleBodyItemClick()}>
            <Td tabIndex = {-1} width = {15} modifier = 'truncate' style = {{paddingLeft : '40px', fontSize : '14px', fontWeight : 'bold', fontStyle : 'normal', paddingTop : "4px", paddingBottom : "4px"}}>
              <Split tabIndex = {-1} style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1} isFilled>{bodyItem.subname}</SplitItem>
                <SplitItem tabIndex = {0}><SystemCounters component = {component} item = {bodyItem}/></SplitItem>
                <SplitItem tabIndex = {0}><SystemOperations component = {component} item = {bodyItem} context = {context}/></SplitItem>
              </Split>
            </Td>
            <Td tabIndex = {-1} width = {10} modifier = 'truncate' style = {{fontSize : '14px', fontWeight : 'bold', fontStyle : 'normal', paddingTop : "4px", paddingBottom : "4px"}} textCenter>
              <ComponentStatus tabIndex = {-1} id = {bodyItem.id} status = {bodyItem.status}/>
            </Td>
            <HeaderValues tabIndex = {-1} id = {id} data = {bodyItem}/>
          </Tr>
          <Details tabIndex = {-1} isHidden = {!isSubBodyExpanded()} isExpanded = {isBodyItemExpanded()} details = {bodyItem.details} />
        </Tbody>
      )
    }

    return (
      <>
        <Tbody tabIndex = {-1}>
          <Tr tabIndex = {0} isStriped style = {{cursor : 'pointer'}} onKeyDown = {handleSubBodyKeyDown} onClick = {() => handleSubBodyClick()}>
            <Td tabIndex = {-1} className = "dxc-c-subbody__name" width = {15} modifier = 'truncate' style = {{paddingTop : "4px", paddingBottom : "4px"}}>
              <Split tabIndex = {-1} style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1} isFilled>{subBody}</SplitItem>
                <SplitItem tabIndex = {0}><SystemCounters component = {component} item = {subBody}/></SplitItem>
                <SplitItem tabIndex = {0}><SystemOperations component = {component} item = {subBody} context = {context}/></SplitItem>
              </Split>
            </Td>
            <Td tabIndex = {-1} className = "dxc-c-subbody__status" width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{status}</Td>
            <EmpytHeaderValues tabIndex = {-1}/>
          </Tr>
        </Tbody>
        {
          bodyItemsWithIndex.map((bodyItemWithIndex, index) => (
            <SubBodyItem tabIndex = {-1} key = {index} id = {id} bodyItem = {bodyItemWithIndex.bodyItem}/>
          ))
        }
      </>
    )
  }

  // ********************************************************************************************************

  const BodyWithSubBodies = (props : any) => {
    log.debug(logPrefix, "BodyWithSubBodies, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
    const data          : MultiValuedData[] = component.computedData;

    const subBodies     : string[]          = data
      .map((bodyItem : MultiValuedData, bodyItemIndex : number) => (bodyItem.name))
      .filter((subBody : string, subBodyIndex : number, array : string[]) => array.indexOf(subBody) === subBodyIndex);

    return (
      <>
      {
        subBodies.map((subBody : string, subBodyIndex : any) => (
            <SubBody tabIndex = {-1} key = {subBodyIndex} id = {id} subBody = {subBody} subBodyIndex = {subBodyIndex} />
        ))
      }
      </>
    )
  }

  // ********************************************************************************************************

  const BodyWithoutSubBodies = (props : any) => {
    log.debug(logPrefix, "BodyWithoutSubBodies, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
    const data          : MultiValuedData[] = component.computedData;

    const BodyItem = (props : any) => {
      const bodyItem      = props.bodyItem;

      const elementBodyItem : string = "BodyItem_" + id + "_" + bodyItem.name;

      const [bodyItemRefresh, setBodyItemRefresh] = useState(false);

      if (getElementExpanded(elementBodyItem) === undefined) {
        const expanded = defaultComponentBodyItemExpanded;
        setElementExpanded(elementBodyItem, expanded);
      };

      const handleBodyItemClick = () => {
        if (window.getSelection()?.toString()) return;
        toggleElementExpanded(elementBodyItem);
        setBodyItemRefresh(!bodyItemRefresh);
      }

      const handleBodyItemKeyDown = (e: React.KeyboardEvent<HTMLTableRowElement>) => {
        if (e.key === KeyTypes.Enter) {
          toggleElementExpanded(elementBodyItem);
          setBodyItemRefresh(!bodyItemRefresh);
        }
      }

      const isBodyItemExpanded = () => { return isElementExpanded(elementBodyItem); }

      const cursor = (bodyItem.details === null || bodyItem.details.length === 0) ? 'context-menu' : 'pointer';

      return (
        <Tbody tabIndex = {-1}>
          <Tr tabIndex = {0} isStriped style = {{cursor : cursor}} onKeyDown = {handleBodyItemKeyDown} onClick = {() => handleBodyItemClick()}>
            <Td tabIndex = {-1} className = "dxc-c-body__name" width = {15} modifier = 'truncate' style = {{paddingTop : "4px", paddingBottom : "4px"}}>
              <Split tabIndex = {-1} style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1} isFilled>{bodyItem.name}</SplitItem>
                <SplitItem tabIndex = {0}><SystemCounters component = {component} item = {bodyItem}/></SplitItem>
                <SplitItem tabIndex = {0}><SystemOperations component = {component} item = {bodyItem} context = {context}/></SplitItem>
              </Split>
            </Td>
            <Td tabIndex = {-1} className = "dxc-c-body__status" width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>
              <ComponentStatus tabIndex = {-1} id = {bodyItem.id} status = {bodyItem.status}/>
            </Td>
            <HeaderValues tabIndex = {-1} id = {id} data = {bodyItem}/>
          </Tr>
          <Details tabIndex = {-1} isExpanded = {isBodyItemExpanded()} details = {bodyItem.details} />
        </Tbody>
      )
    }

    return (
      <>
      {
        data.map((bodyItem : MultiValuedData, bodyItemIndex : any) => (
          <BodyItem tabIndex = {-1} key = {bodyItemIndex} bodyItem = {bodyItem}/>
        ))
      }
      </>
    )
  }

  // ********************************************************************************************************

  const BodyWithSimpleData = (props : any) : any => {
    log.debug(logPrefix, "BodyWithSimpleData, id: ", props.id);

    const id            : string             = props.id;
    const component     : Component          = gtmonitor[id];
    const data          : SimpleData[]       = component.computedData;
    const title         : string             = component.title;

    const counterId = component.headerId;

    let counterData : SimpleData[] = [];

    if (counterId !== null) {
      const counterComponent = gtmonitor[counterId];
      counterData = (counterComponent.multiValued) ? counterComponent.computedData.details
                                                   : counterComponent.computedData;
    }

    if (counterData.length === 0) {
      return (
        <Tbody tabIndex = {-1}>
          <Details tabIndex = {-1} isExpanded = {true} details = {data}/>
        </Tbody>
      )
    }

    const headers = component.headerAttributes.map((headerAttribute : string) => {
      const item = counterData.find((item : SimpleData) => (item.name === headerAttribute))
      return (item === undefined) ? null : item.value;
    })

    let status = null;
    if (component.statusAttribute !== null) {
      try {
        const item = counterData.find((item : SimpleData) => (item.name === component.statusAttribute));
        status = (item === undefined) ? null : item.value;
      }
      catch (e) {}
    }

    const bodyItem : MultiValuedData = {
      id          : id,
      name        : title,
      subname     : " ",
      status      : status,
      headers     : headers,
      details     : data
    }

    const [bodyRefresh, setBodyRefresh] = useState(false);

    const elementBodyDetails : string = "BodyWithSimpleDataDetails_" + id;

    if (getElementExpanded(elementBodyDetails) === undefined) {
      const expanded = defaultComponentDetailsExpanded;
      setElementExpanded(elementBodyDetails, expanded);
    };

    const handleBodyDetailsClick = () => {
      if (window.getSelection()?.toString()) return;
      toggleElementExpanded(elementBodyDetails);
      setBodyRefresh(!bodyRefresh);
    }

    const handleBodyDetailsKeyDown = (e: React.KeyboardEvent<HTMLTableRowElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleElementExpanded(elementBodyDetails);
        setBodyRefresh(!bodyRefresh);
      }
    }

    const isBodyDetailsExpanded = () => { return isElementExpanded(elementBodyDetails); }

    const cursor = (data === null || data.length === 0) ? 'context-menu' : 'pointer';

    return (
      <Tbody tabIndex = {-1}>
        <Tr tabIndex = {0} isStriped style = {{cursor : cursor}} onKeyDown = {handleBodyDetailsKeyDown} onClick = {() => handleBodyDetailsClick()}>
          <Td tabIndex = {-1} className = "dxc-c-body__name" width = {15} modifier = 'truncate' style = {{paddingTop : "4px", paddingBottom : "4px"}}>
            <Split tabIndex = {-1} style = {{alignItems : "center"}}>
              <SplitItem tabIndex = {-1} isFilled>{bodyItem.name}</SplitItem>
              <SplitItem tabIndex = {0}><SystemCounters component = {component} item = {bodyItem}/></SplitItem>
              <SplitItem tabIndex = {0}><SystemOperations component = {component} item = {bodyItem} context = {context}/></SplitItem>
            </Split>
          </Td>
          <Td tabIndex = {-1} className = "dxc-c-body__status" width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>
            <ComponentStatus tabIndex = {-1} id = {bodyItem.id} status = {bodyItem.status}/>
          </Td>
          <HeaderValues tabIndex = {-1} id = {id} data = {bodyItem}/>
        </Tr>
        <Details tabIndex = {-1} isExpanded = {isBodyDetailsExpanded()} details = {data} />
     </Tbody>
    )
  }

  // ********************************************************************************************************
    
  const BodyWithMultiValuedData = (props : any) : any => {
    log.debug(logPrefix, "BodyWithMultiValuedData, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
    const hasSubBodies  : boolean           = (component.regexSubname !== null);

    if (hasSubBodies) return <BodyWithSubBodies    tabIndex = {-1} id = {id}/>
    else              return <BodyWithoutSubBodies tabIndex = {-1} id = {id}/>
  }

  // ********************************************************************************************************
    
  const BodyFromHeaders = (props : any) : any => {
    log.debug(logPrefix, "BodyFromHeaders, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];
   
    if (!component.bodyFromHeaders)
      return <></>

    const headerId          = component.headerId;
    const headerAttributes  = component.headerAttributes;
    const headerNames       = component.headerNames;

    let headerData : SimpleData[] = [];

    if (headerId !== null) {
      const headerComponent = gtmonitor[headerId];
      headerData = (headerComponent.multiValued) ? headerComponent.computedData.details
                                           : headerComponent.computedData;
    }

    const bodyData : any[] = [];
    headerAttributes.map((headerAttribute : string, index : number) => {
      let itemName = null;
      try {
        itemName = headerNames[index];
        if (itemName === undefined) itemName = null;
      }
      catch (e) {};
  
      if (itemName !== null) {
        let itemValue = null;
        const item = headerData.find((item : SimpleData) => (item.name === headerAttribute))
        if (item !== undefined && item !== null)
          itemValue = item.value;
        if (itemValue === undefined) itemValue = null;
        bodyData.push([itemName, itemValue]);
      }
    })

    return (
      <CardBody tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
        <Table tabIndex = {-1}>
          <Tbody tabIndex = {-1}>
          {
            bodyData.map ((bodyDataItem : any, bodyDataItemIndex : number) =>
              <Tr tabIndex = {0} key = {bodyDataItemIndex} isStriped style = {{cursor : 'pointer'}}>
                <Td tabIndex = {-1} className = "dxc-c-body__name" width = {10} modifier = 'truncate' style = {{paddingTop : "4px", paddingBottom : "4px"}}>
                  {(bodyDataItem[0] === null) ? " " : bodyDataItem[0]}
                </Td>
                <Td tabIndex = {-1} width = {90} modifier = 'truncate' style = {{fontSize : '14px', fontWeight : 'normal', fontStyle : 'normal', paddingTop : "4px", paddingBottom : "4px"}}>
                  {(bodyDataItem[1] === null) ? " " : valueToDisplay(bodyDataItem[1])}
                </Td>
              </Tr>
            )
          }
          </Tbody>
        </Table>
      </CardBody>
    )
  }

  // ********************************************************************************************************

  const Body = (props : any) : any => {
    log.debug(logPrefix, "Body, id: ", props.id);

    const id            : string            = props.id;
    const component     : Component         = gtmonitor[id];

    if (component.multiValued) return <BodyWithMultiValuedData tabIndex = {-1} id = {id}/>
    else                       return <BodyWithSimpleData tabIndex = {-1} id = {id}/>
  }

  // ********************************************************************************************************

  const BodyWithExpandCollapseAll = (props : any) : any => {
    const bodyId        : string            = props.bodyId;
    const type          : string            = props.type;
    const component     : Component         = gtmonitor[bodyId];
      
    const [bodyWithExpandCollapseAllRefresh, setBodyWithExpandCollapseAllRefresh] = useState<boolean>(true);

    const refExpandAll    = useRef<HTMLInputElement>(null);
    const refCollapseAll  = useRef<HTMLInputElement>(null);

    const triggerBodyWithExpandCollapseAllRefresh = () => {
      setBodyWithExpandCollapseAllRefresh(!bodyWithExpandCollapseAllRefresh);
    }
  
    const handleExpandAll = () => {
      log.debug(logPrefix, "ExpandCollapseAll OnExpandAll, id: ", bodyId, ", type: ", type);
      const elementPrefix = type + "_" + bodyId + "_";
      const keys = Array.from(expandedElements.current.keys());
      keys.forEach((key) => {
        if (key.startsWith(elementPrefix)) expandedElements.current.set(key, true)
      })
    }
  
    const handleCollapseAll = () => {
      log.debug(logPrefix, "ExpandCollapseAll OnCollapseAll, id: ", bodyId, ", type: ", type);
      const elementPrefix = type + "_" + bodyId + "_";
      const keys = Array.from(expandedElements.current.keys());
      keys.forEach((key) => {
        if (key.startsWith(elementPrefix)) expandedElements.current.set(key, false)
      })
    }
    
    const handleExpandAllClick = () => {
      log.debug(logPrefix, "ExpandCollapseAll handleExpandAllClick, id: ", bodyId, ", type: ", type);
      if (window.getSelection()?.toString()) return;
      handleExpandAll();
      triggerBodyWithExpandCollapseAllRefresh();
    }
  
    const handleExpandAllKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
      log.debug(logPrefix, "ExpandCollapseAll handleExpandAllKeyDown, id: ", bodyId, ", type: ", type);
      if (e.key === KeyTypes.Enter) {
        handleExpandAll();
    //      if (refCollapseAll !== null && refCollapseAll.current !== null)
    //        refCollapseAll.current.focus();   // !!! focus triggers click
      }
    }
   
    const handleCollapseAllClick = () => {
      log.debug(logPrefix, "ExpandCollapseAll handleCollapseAllClick, id: ", bodyId, ", type: ", type);
      if (window.getSelection()?.toString()) return;
      handleCollapseAll();
      triggerBodyWithExpandCollapseAllRefresh();
    }
  
    const handleCollapseAllKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
      log.debug(logPrefix, "ExpandCollapseAll handleCollapseAllKeyDown, id: ", bodyId, ", type: ", type);
      if (e.key === KeyTypes.Enter) {
        handleCollapseAll();
    //      if (refExpandAll !== null && refExpandAll.current !== null)
    //        refExpandAll.current.focus();   // !!! focus triggers click
      }
    }
  
    const handleExpandAllFocus = (e: React.FocusEvent<HTMLButtonElement, Element>) => {
      log.debug(logPrefix, "ExpandCollapseAll handleExpandAllFocus, id: ", bodyId, ", type: ", type);
    }
  
    const handleCollapseAllFocus = (e: React.FocusEvent<HTMLButtonElement, Element>) => {
      log.debug(logPrefix, "ExpandCollapseAll handleCollapseAllFocus, id: ", bodyId, ", type: ", type);
    }

    return (
      <CardBody tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
        <Table tabIndex = {-1} variant= 'compact'>
          <Thead tabIndex = {-1} noWrap style = {{paddingTop : "4px", paddingBottom : "4px"}}>
            <Tr tabIndex = {-1} id = {bodyId + "BodyHeaders"} style = {{paddingTop : "4px", paddingBottom : "4px"}}>
              <Th tabIndex = {-1} aria-label = { "expandcollapseall-" + bodyId + "Body" } width = {10} style = {{paddingTop : "4px", paddingBottom : "4px"}}>
                {
                  ((component.multiValued) &&
                  (
                    <Flex tabIndex = {-1}>
                      <FlexItem tabIndex = {-1}><Button ref = {refExpandAll}   tabIndex = {0} variant = 'link' size = 'sm' icon = {<FolderOpenIcon tabIndex = {-1}/>} isInline = {true} onFocus = {handleExpandAllFocus}   onKeyDown = {handleExpandAllKeyDown}   onClick = {handleExpandAllClick}>Expand all</Button></FlexItem>
                      <FlexItem tabIndex = {-1}><Button ref = {refCollapseAll} tabIndex = {0} variant = 'link' size = 'sm' icon = {<FolderIcon     tabIndex = {-1}/>} isInline = {true} onFocus = {handleCollapseAllFocus} onKeyDown = {handleCollapseAllKeyDown} onClick = {handleCollapseAllClick}>Collapse all</Button></FlexItem>
                    </Flex>
                  )) ||
                  (
                    <Flex tabIndex = {-1}>
                      <FlexItem tabIndex = {-1}>{" "}</FlexItem>
                      <FlexItem tabIndex = {-1}>{" "}</FlexItem>
                    </Flex>
                  )
                }
              </Th>
              <Th tabIndex = {-1} aria-label = { "expandcollapseall-" + bodyId + "Body" } width = {10} modifier = 'truncate' textCenter style = {{paddingTop : "4px", paddingBottom : "4px"}}>{" "}</Th>
              <HeaderNames tabIndex = {-1} id = {bodyId}/>
            </Tr>
          </Thead>
          <Body tabIndex = {-1} id = {bodyId}/>
        </Table>
      </CardBody>
    )
  }

  // ********************************************************************************************************

  const ComponentBody = (props : any) => {
    log.debug(logPrefix, "ComponentBody, bodyId: ", props.bodyId);

    const bodyId      : string            = props.bodyId;

    if (bodyId === null)
      return (<></>);

    const componentBody                   = gtmonitor[bodyId];

    const title       : string            = componentBody.title;

    const element     : string            = "ComponentBody_" + bodyId;

    const [componentBodyRefresh, setComponentBodyRefresh] = useState(false);

    const triggerComponentBodyRefresh = () => { setComponentBodyRefresh(!componentBodyRefresh) }

    if (getElementExpanded(element) === undefined) {
      const expanded = defaultComponentBodyExpanded;
      setElementExpanded(element, expanded);
    };

    const handleComponentBodyExpandClick = () => {
      if (window.getSelection()?.toString()) return;
      toggleElementExpanded(element);
      triggerComponentBodyRefresh();
    }

    const handleComponentBodyExpandKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleElementExpanded(element);
        triggerComponentBodyRefresh();
      }
    }

    const isComponentBodyExpanded = () => { return isElementExpanded(element); }

    if (gtmonitor[bodyId].multiValued) {
      const component     : Component         = gtmonitor[bodyId];
      const hasSubBodies  : boolean           = (component.regexSubname !== null);
      const type          : string            = hasSubBodies ? "SubBody" : "BodyItem";

      return (
        <Card tabIndex = {-1} key = {bodyId + "Body"} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {isComponentBodyExpanded()} >
          <CardHeader tabIndex = {0} style = {{cursor : 'pointer', paddingTop : "16px", paddingBottom : "0px"}} isToggleRightAligned = {true} onKeyDown = {handleComponentBodyExpandKeyDown} onClick = {() => handleComponentBodyExpandClick()}>
            <CardTitle tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
              <TextContent tabIndex = {-1}>
                <Text tabIndex = {-1} align = 'left' component = {TextVariants.h4}>{title}</Text>
              </TextContent>
            </CardTitle>
          </CardHeader>
          <CardExpandableContent tabIndex = {-1}>
            <BodyWithExpandCollapseAll tabIndex = {-1} key = {bodyId + "BodyWithEpandCollapseAll"} bodyId = {bodyId} type = {type}/>
          </CardExpandableContent>
        </Card>
      )
    }
    else {
      return (
        <Card tabIndex = {-1} key = {bodyId + "Body"} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {true} >
          <CardExpandableContent tabIndex = {-1}>
            <BodyWithExpandCollapseAll tabIndex = {-1} key = {bodyId + "BodyWithEpandCollapseAll"} bodyId = {bodyId} type = "BodyItem"/>
          </CardExpandableContent>
        </Card>
      )
    }
  }

  // ********************************************************************************************************

  const ComponentBodies = (props : any) => {
    log.debug(logPrefix, "ComponentBodies, id: ", props.id);

    const id              = props.id;
    const component       = gtmonitor[id];
    const bodyIds         = component.bodyIds;

    if (bodyIds === null || bodyIds.length === 0)
      return <></>;

    return (
      <>
      {
        bodyIds.map((bodyId : string, bodyIdIndex : any) => <ComponentBody tabIndex = {-1} key = {bodyIdIndex} bodyId = {bodyId}/>)
      }
      </>
    )
  }

  // ********************************************************************************************************

  const ComponentWithData = (props : any) => {
    log.debug(logPrefix, "ComponentWithData, id: ", props.id);

    const id              : string            = props.id;
    const component       : Component         = gtmonitor[id];
    const data            : SimpleData[]      = component.computedData;
    const bodyIds         : string[]          = component.bodyIds;
    const statusAttribute : string | null     = component.statusAttribute;

    const element         : string            = "ComponentWithData_" + id;

    const [componentRefresh, setComponentRefresh] = useState(false);

    if (getElementExpanded(element) === undefined) {
      const expanded = (component.bodyFromHeaders) ? defaultComponentWithBodyExpanded :
                       (bodyIds !== null && bodyIds.length > 0) ? defaultComponentWithBodyExpanded
                                                                : defaultComponentWithoutBodyExpanded;
      setElementExpanded(element, expanded);
    };

    const handleComponentClick = () => {
      if (window.getSelection()?.toString()) return;
      toggleElementExpanded(element);
      setComponentRefresh(!componentRefresh);
    }
  
    const handleComponentKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === KeyTypes.Enter) {
        toggleElementExpanded(element);
        setComponentRefresh(!componentRefresh);
      }
    }

    const isComponentExpanded = () => { return isElementExpanded(element); }

    let status = data.find((item : SimpleData) => (item.name === statusAttribute));
    status = (status === null || status === undefined) ? null : status.value;

    const bodyItem : MultiValuedData = {
      id          : id,
      name        : component.title,
      subname     : " ",
      status      : status,
      headers     : [],
      details     : data
    }
    return (
      <Card tabIndex = {-1} key = {id} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {isComponentExpanded()}>
        <CardHeader tabIndex = {0} style = {{cursor : 'pointer', paddingTop : "20px", paddingBottom : "12px"}} isToggleRightAligned = {true} onKeyDown = {handleComponentKeyDown} onClick = {() => handleComponentClick()}>
          <CardTitle tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
            <Split hasGutter isFilled style = {{ alignItems: 'center' }}>
              <SplitItem style = {{ width: "15%" }}>
                <TextContent tabIndex = {-1}>
                  <Text tabIndex = {-1} align = 'left' component = {TextVariants.h3}>{component.title}</Text>
                </TextContent>
              </SplitItem>
              <SplitItem style = {{ width: "5%" }}>
                <SystemOperations component = {component} item = {bodyItem} context = {context} />
              </SplitItem>
              <SplitItem style = {{ width: "5%" }}>
                <ComponentStatus id = {id} status = {status} />
              </SplitItem>
            </Split>
          </CardTitle>
        </CardHeader>
        <CardExpandableContent tabIndex = {-1}>
          <CardBody tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
            <ComponentDetails tabIndex = {-1} id = {id}/>
            <BodyFromHeaders  tabIndex = {-1} id = {id}/>
            <ComponentBodies  tabIndex = {-1} id = {id}/>
          </CardBody>
        </CardExpandableContent>
      </Card>
    )
  }

  // ********************************************************************************************************

  const ComponentWithoutData = (props : any) => {
    log.debug(logPrefix, "ComponentWithoutData, id: ", props.id);

    const id              : string          = props.id;
    const component       : Component       = gtmonitor[id];

    return (
      <Card tabIndex = {-1} key = {id} isFullHeight = {true} isCompact = {true} isPlain = {false} isExpanded = {false}>
        <CardHeader tabIndex = {-1} style = {{cursor : 'pointer', paddingTop : "20px", paddingBottom : "12px"}} isToggleRightAligned = {true} onClick = {() => {}}>
          <CardTitle tabIndex = {-1} style = {{paddingTop : "0px", paddingBottom : "0px"}}>
            <Grid tabIndex = {-1} hasGutter = {true}>
              <GridItem tabIndex = {-1} span = {1}>
                <TextContent tabIndex = {-1}>
                  <Text tabIndex = {-1} align = 'left' component = {TextVariants.h3}>{component.title}</Text>
                </TextContent>
              </GridItem>
              <GridItem tabIndex = {-1} span = {2} style = {{alignItems : "center", alignContent : "center"}}>
                <ComponentStatus tabIndex = {-1} id = {id} status = {null}/>
              </GridItem>
            </Grid>
          </CardTitle>
        </CardHeader>
      </Card>
    )
  }

  // ********************************************************************************************************

  const Component = (props : any) => {
    log.debug(logPrefix, "Component, id: ", props.id);

    const id              : string            = props.id;
    const component       : Component         = gtmonitor[id];
    const data            : ComponentData[]   = component.computedData;

    if (!Array.isArray(data)) {
      log.error(logPrefix, "Component, data is not an array, id: ", id);
      return <></>
    }

    if (data.length > 0 || component.ignoreData)
      return <ComponentWithData    tabIndex = {-1} key = {id} id = {id}/>
    else
      return <ComponentWithoutData tabIndex = {-1} key = {id} id = {id}/>
  }

  // ********************************************************************************************************

  const [context, setContext] = useState<any | null>(null);

  async function retrieveContext() {
    const context = await getContext();
    setContext(context);
    log.debug(logPrefix, "context: ", context);
  }

  // ********************************************************************************************************

  async function computeGMonitor() {
    await retrieveContext();

    await refreshJolokia();
/*
    let hawtconfig : Hawtconfig | null = await configManager.getHawtconfig();
    if (hawtconfig === undefined)
      hawtconfig = null;
    setHawtconfig(hawtconfig);
*/
    let gtmonitorNew = {...gtmonitor};

//  await new Promise(f => setTimeout(f, 10000));

    const refreshRetrievedDataPromises = Object.values(gtmonitorNew).map((component : Component) => retrieveData(component))
    await Promise.all(refreshRetrievedDataPromises);

    const refreshComputedDataPromises = Object.values(gtmonitorNew).map((component : Component) => computeData(gtmonitorNew, component))
    await Promise.all(refreshComputedDataPromises);

    setGtMonitor(gtmonitorNew);
  }

  // ********************************************************************************************************
 
  log.debug(logPrefix, "Displaying GtMonitor ****************************************************************************");

  return (
    <GraphTalkComponent tabIndex = {-1} title = "GtMonitor" onCompute = {computeGMonitor}>
      <GraphTalkComponentDiv hasScroll>
        <Stack tabIndex = {-1}>
        {
          Components.map((id : string) => (
            <StackItem tabIndex = {-1} key = {id}>
              <Component tabIndex = {-1} key = {id} id = {id}/>
            </StackItem>
          ))
        }
        </Stack>
      </GraphTalkComponentDiv>
    </GraphTalkComponent>
  )

}

// ********************************************************************************************************

export default GtMonitor;
