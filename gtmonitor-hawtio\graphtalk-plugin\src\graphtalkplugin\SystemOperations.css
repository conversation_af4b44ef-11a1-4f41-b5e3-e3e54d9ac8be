
.system-operations-common-style {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.system-operations-row-style {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 5px;
}

.system-operations-label-style {
  font-size: 14px;
  font-weight: normal;
  min-width: 120px;
  flex-shrink: 0;
}

.system-operations-field-style {
  flex: 1;
  max-width: 200px;
}

.system-operations-number-field-style {
  width: 170px;
  max-width: 170px;
}

.system-operations-number-field-wrapper {
  width: auto;
  max-width: 170px;
}

.system-operations-desc-style {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-left: 135px;
}

.system-operations-modal {
  padding: 20px;
}

.system-operations-content {
  margin-top: 20px;
}

.system-operations-selected-method {
  margin-top: 15px;
  padding: 12px;
  border: 1px solid #d2d2d2;
  border-radius: 4px;
  background-color: #f8f8f8;
}

.system-operations-table {
  margin-bottom: 20px;
  font-size: 12px; /* Compact font size */
  max-height: 180px; /* Further reduced to ensure exactly 10 rows visible */
  overflow-y: auto;
}

.system-operations-table th {
  padding: 3px 6px; /* Compact header padding */
  vertical-align: top;
  line-height: 1.1;
  height: 24px; /* Smaller header height */
}

.system-operations-table td {
  padding: 2px 6px; /* Very compact row padding */
  vertical-align: top;
  line-height: 1.1;
  height: 22px; /* Smaller row height */
}

.system-operations-table th {
  font-weight: 600;
  background-color: #f5f5f5;
  border-bottom: 2px solid #d2d2d2;
}

.system-operations-table-row {
  cursor: pointer;
}

.system-operations-table-row:hover {
  background-color: #f5f5f5;
}



.system-operations-execute-button {
  margin-top: 10px;
}

.system-operations-desc-style {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-left: 135px;
}

.system-operations-button {
  padding: 0px 0px 0px 0px;
}

.system-operations-icon {
  padding: 0px 0px 0px 0px;
}
