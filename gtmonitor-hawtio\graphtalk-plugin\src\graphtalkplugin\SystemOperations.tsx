import React from 'react'
import { useState } from 'react'

import { Button, Flex, FlexItem, Icon, Modal, Tooltip, TextInput, Checkbox, Title } from '@patternfly/react-core'
import { Table, Thead, Tr, Th, Tbody, Td } from '@patternfly/react-table'
import { EllipsisVIcon } from '@patternfly/react-icons'

import { jolokiaService } from '@hawtio/react'
import { Logger } from '@hawtio/react'
import { IJolokiaSimple } from '@jolokia.js/simple'

import {
  ArgumentInfo,
  OptionItem,
  Props,
  parentNameMap,
  removeSpaces,
  cleanToFlatName,
  simplifyType,
  isGenericDescription,
  generateAdminPath,
  createOperationSignature,
  prepareArguments
} from './SystemOperationsHelpers'

import './SystemOperations.css'


// ********************************************************************************************************
// ********************************************************************************************************
// SystemOperations
// ********************************************************************************************************
// ********************************************************************************************************

export const SystemOperations = (props: Props): any => {
  const systemOperations  = props.component?.systemOperations;
  const item              = props?.item;
  const context           = props.context;

  if (!context?.canAccessCounters)
    return <></>

  if (!systemOperations)
    return <></>

  const nodeTitle = cleanToFlatName(props.component?.title);
  const nodeName = removeSpaces((props?.item?.subname === null || props?.item?.subname === undefined || props?.item?.subname === " ") ? props?.item?.name : props?.item?.subname);

  const mappedNodeTitle = parentNameMap[nodeTitle] || nodeTitle;
  
  function findOpsFromOpByString(mbeanTree: any[], targetName: any, nodeTitle?: string): OptionItem[] {
    const results: OptionItem[] = [];

    function searchNode(node: any) {
      if (!node) return null;

      if (nodeTitle && node.name === nodeTitle) {
        const foundInNode = searchInNode(node, targetName);
        if (foundInNode) return foundInNode;
      }

      if (!nodeTitle && node.name === targetName) return node;
      if (!nodeTitle && node.parent?.name === targetName) return node.parent;

      if (Array.isArray(node.children)) {
        for (const child of node.children) {
          const found: any = searchNode(child);
          if (found) return found;
        }
      }

      return null;
    }

    function searchInNode(parentNode: any, targetName: string): any {
      if (!parentNode) return null;

      if (parentNode.name === targetName) return parentNode;

      if (Array.isArray(parentNode.children)) {
        for (const child of parentNode.children) {
          const found = searchInNode(child, targetName);
          if (found) return found;
        }
      }

      return null;
    }

    let matchedNode = null;

    for (const node of mbeanTree) {
      matchedNode = searchNode(node);
      if (matchedNode) break;
    }

    if (matchedNode?.mbean?.opByString) {
      const opMap = matchedNode.mbean.opByString;

      for (const [signature, data] of Object.entries(opMap)) {
        const methodName = signature.split("(")[0];
        const argString = signature.match(/\((.*)\)/)?.[1];
        const description = (data as any)?.desc || "No description available";

        if (!argString || argString.trim() === "") {
          results.push({
            signature: `${methodName}()`,
            description: description,
            hasArgs: false,
            sourceNode: matchedNode
          });
        } else {
          const argList = argString.split(",").map(arg => arg.trim());
          const shortTypes = argList.map(argType => simplifyType(argType));

          const argumentsInfo: ArgumentInfo[] = [];
          const argsData = (data as any)?.args;

          if (argsData && Array.isArray(argsData)) {
            argsData.forEach((argData: any, index: number) => {
              argumentsInfo.push({
                name: argData?.name || `arg${index}`,
                type: shortTypes[index] || 'Unknown',
                description: argData?.desc || 'No description'
              });
            });
          } else {
            shortTypes.forEach((type, index) => {
              argumentsInfo.push({
                name: `arg${index}`,
                type: type,
                description: 'No description available'
              });
            });
          }

          results.push({
            signature     : `${methodName}(${shortTypes.join(", ")})`,
            description   : description,
            hasArgs       : true,
            arguments     : argumentsInfo,
            sourceNode    : matchedNode
          });
        }
      }
    }

    return results;
  }

  //const operationsList = findOpsFromOpByString(context.gtMonitorNode.children, nodeName, mappedNodeTitle);
  const operationsList = findOpsFromOpByString(context.mbeanTree.tree, nodeName, mappedNodeTitle);

  let serverPoolName = null;
  let serverName     = null;

  if (typeof item === "string") {
    serverPoolName = item.trim();
  }
  else {
    serverPoolName = (item?.name === null || item?.name === undefined) ? null : item?.name?.trim();
    serverName = (item?.subname === null || item?.subname === undefined) ? null : item?.subname?.trim();
  }

  serverPoolName = (serverPoolName === "") ? null : serverPoolName;
  serverName = (serverName === "") ? null : serverName;

  if (serverPoolName !== null && serverName !== null)
    return <SystemOperationsButtonModal serverPoolName = {serverPoolName} serverName = {serverName} operationsList = {operationsList} />
  if (serverPoolName !== null)
    return <SystemOperationsButtonModal serverPoolName = {serverPoolName} nodeName = {nodeName} operationsList = {operationsList} />
  if (serverName !== null)
    return <SystemOperationsButtonModal serverName = {serverName} nodeName = {nodeName} operationsList = {operationsList} />

  return <SystemOperationsButtonModal nodeName = {nodeName} operationsList = {operationsList} />
}

// ********************************************************************************************************

export const SystemOperationsButtonModal: React.FunctionComponent<Props> = (props) => {

  const [isSystemOperationsOpen, setIsSystemOperationsOpen] = useState(false);
  const [selectedOperation, setSelectedOperation]           = useState<OptionItem | null>(null);
  const [argumentValues, setArgumentValues]                 = useState<Record<string, any>>({});
  const [isExecuting, setIsExecuting]                       = useState(false);
  const [resultModal, setResultModal]                       = useState<{isOpen: boolean, title: string, content: string, isError: boolean}>({isOpen: false, title: '', content: '', isError: false});

  const showResultModal = (title: string, content: string, isError: boolean = false) => {
    setResultModal({isOpen: true, title, content, isError});
  };

  const closeResultModal = () => {
    setResultModal({isOpen: false, title: '', content: '', isError: false});
  };

  // Jolokia setup
  const log = Logger.get("graphtalk-SystemOperations");
  const logPrefix = "SystemOperations";
  let jolokia: IJolokiaSimple | null = null;



  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  async function executeOperation(operation: string, args: any[] = [], sourceNode?: any) {
    setIsExecuting(true);
    await refreshJolokia();

    try {
      if (jolokia !== null) {
        const adminPath = generateAdminPath(sourceNode);

        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          log.debug(logPrefix, "executeOperation, operation: ", operation, "args: ", args, "mbean: ", mbean);

          let result;
          if (args.length === 0) {
            result = await jolokia.execute(mbean, operation);
          } else {
            result = await jolokia.execute(mbean, operation, ...args);
          }

          log.debug(logPrefix, "executeOperation, result: ", result);

          if (result !== null && result !== undefined) {
            showResultModal('Operation executed successfully', `${result}`);
          } else {
            showResultModal('Operation executed successfully', 'Operation completed without return value.');
          }

          // Trigger refresh of parent component
          if (props.onRefresh) {
            log.debug(logPrefix, "executeOperation, calling onRefresh callback");
            await props.onRefresh();
            log.debug(logPrefix, "executeOperation, onRefresh callback completed");
          } else {
            log.warn(logPrefix, "executeOperation, no onRefresh callback provided");
          }
        }
      }
    } catch (error) {
      log.error(logPrefix, "executeOperation, error: ", error);
      showResultModal('Error executing operation', `${error}`, true);
    } finally {
      setIsExecuting(false);
    }
  }



  async function handleExecute() {
    if (!selectedOperation) return;

    const methodName    = selectedOperation.signature.split('(')[0];
    const args          = selectedOperation.arguments || [];
    const sourceNode    = selectedOperation.sourceNode;

    const operation     = createOperationSignature(methodName, args);
    const operationArgs = prepareArguments(args, argumentValues);

    await executeOperation(operation, operationArgs, sourceNode);
  }

  const handleSystemOperationsClick = () => {
    setIsSystemOperationsOpen(true);
  }

  const handleSystemOperationsClose = () => {
    setIsSystemOperationsOpen(false);
    setSelectedOperation(null);
    setArgumentValues({});
  }

  const onOperationSelect = (operation: OptionItem) => {
    setSelectedOperation(operation);

    const initialValues: Record<string, any> = {};
    if (operation?.arguments) {
      operation.arguments.forEach((arg, index) => {
        const key = `arg_${index}`;
        if (arg.type === 'Boolean' || arg.type === 'boolean') {
          initialValues[key] = false;
        } else if (arg.type === 'Integer' || arg.type === 'int' || arg.type === 'Long' || arg.type === 'long') {
          initialValues[key] = 0;
        } else {
          initialValues[key] = '';
        }
      });
    }
    setArgumentValues(initialValues);

    setTimeout(() => {
      const argumentsSection = document.querySelector('.system-operations-selected-method');
      if (argumentsSection) {
        argumentsSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }, 100);
  }

  const handleArgumentChange = (argIndex: number, value: any) => {
    const key = `arg_${argIndex}`;
    setArgumentValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const renderArgumentField = (arg: ArgumentInfo, index: number) => {
    const key = `arg_${index}`;
    const value = argumentValues[key];

    const displayDescription = isGenericDescription(arg.description)
      ? `Type: ${simplifyType(arg.type)}`
      : arg.description + ` (Type: ${simplifyType(arg.type)})`;

    if (arg.type === 'String' || arg.type === 'string') {
      return (
        <div key = {key} className = "system-operations-common-style">
          <div className = "system-operations-row-style">
            <label className = "system-operations-label-style">{arg.name}</label>
            <TextInput
              id="field-style"
              className   = "system-operations-field-style"
              value       = {value || ''}
              onChange    = {(_event, val) => handleArgumentChange(index, val)}
              placeholder = {`Enter ${simplifyType(arg.type)}`}
            />
          </div>
          <div className = "system-operations-desc-style">{displayDescription}</div>
        </div>
      );
    }

    if (arg.type === 'Integer' || arg.type === 'int' || arg.type === 'Long' || arg.type === 'long') {
      return (
        <div key = {key} className = "system-operations-common-style">
          <div className = "system-operations-row-style">
            <label className = "system-operations-label-style">{arg.name}</label>
            <div className ="system-operations-number-field-wrapper">
              <TextInput
                id="number-field-style"
                className = "system-operations-number-field-style"
                style     = {{textAlign: 'left'}}
                type      = "number"
                value     = {value || 0}
                onChange  = {(_event, val) => handleArgumentChange(index, parseInt(val) || 0)}
                min       = {0}
              />
            </div>
          </div>
          <div className = "system-operations-desc-style">{displayDescription}</div>
        </div>
      );
    }

    if (arg.type === 'Boolean' || arg.type === 'boolean') {
      return (
        <div key = {key} className = "system-operations-common-style">
          <div className = "system-operations-row-style">
            <label className = "system-operations-label-style">{arg.name}</label>
            <Checkbox
              id        = {key}
              isChecked = {value || false}
              onChange  = {(_event, checked) => handleArgumentChange(index, checked)}
            />
          </div>
          <div className = "system-operations-desc-style">{displayDescription}</div>
        </div>
      );
    }

    // Default case for unknown types
    return (
      <div key={key} className = "system-operations-common-style">
        <div className = "system-operations-row-style">
          <label className = "system-operations-label-style">{arg.name}</label>
          <TextInput
            id="field-style"
            className   = "system-operations-field-style"
            value       = {value || ''}
            onChange    = {(_event, val) => handleArgumentChange(index, val)}
            placeholder = {`Enter ${simplifyType(arg.type)}`}
          />
        </div>
        <div className = "system-operations-desc-style">{displayDescription}</div>
      </div>
    );
  };

  // Don't show the button if there are no operations
  if (!props.operationsList || props.operationsList.length === 0) {
    return <></>;
  }

  return (
    <Flex tabIndex = {-1}>
      <FlexItem tabIndex = {-1} onClick = {(e: any) => { e.stopPropagation(); }}>
        <Tooltip content = {"Operations"}>
          <Button
            tabIndex  = {0}
            variant   = "plain"
            size      = "default"
            onClick   = {handleSystemOperationsClick}
            icon      = {<Icon status = "info" iconSize = "md" className = "system-operations-icon"><EllipsisVIcon /></Icon>}
            className = "system-operations-button"
          />
        </Tooltip>
        <Modal
          isOpen            = {isSystemOperationsOpen}
          onClose           = {handleSystemOperationsClose}
          width             = "50%"
          height            = "60%"
          hasNoBodyWrapper  = {true}
          className         = "dialog"
          aria-label        = "SystemOperationsButtonModal"
        >
          <div className = "system-operations-modal">

            <div className = "system-operations-content">
              <Title tabIndex = {-1} headingLevel = "h4" size = "xl" style = {{paddingBottom: "12px"}}>
                {props.serverName || props.serverPoolName || props.nodeName || 'System'}
              </Title>
              <div style = {{maxHeight: '350px', overflowY: 'auto'}}>
                <Table className = "system-operations-table">
                  <Thead>
                    <Tr>
                      <Th>Method</Th>
                      <Th>Description</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {props.operationsList?.map((operation, index) => (
                    <Tr
                      key = {index}
                      isClickable
                      isRowSelected = {selectedOperation?.signature === operation.signature}
                      onRowClick = {() => onOperationSelect(operation)}
                      className = "system-operations-table-row"
                    >
                      <Td>{operation.signature}</Td>
                      <Td>{operation.description}</Td>
                    </Tr>
                  ))}
                  </Tbody>
                </Table>
              </div>

              {selectedOperation && (
                <div className = "system-operations-selected-method">

                  {!selectedOperation.hasArgs && (
                    <Button
                      variant     = "danger"
                      onClick     = {handleExecute}
                      isLoading   = {isExecuting}
                      isDisabled  = {isExecuting}
                    >
                      {isExecuting ? 'Executing...' : 'Execute'}
                    </Button>
                  )}

                  {selectedOperation.hasArgs && selectedOperation.arguments && (
                    <div>
                      {selectedOperation.arguments.map((arg, index) => renderArgumentField(arg, index))}
                      <Button
                        variant     = "danger"
                        onClick     = {handleExecute}
                        isLoading   = {isExecuting}
                        isDisabled  = {isExecuting}
                        className   = "system-operations-execute-button"
                      >
                        {isExecuting ? 'Executing...' : 'Execute'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Modal>
      </FlexItem>

      {/* Result Modal */}
      <Modal
        variant     = "medium"
        isOpen      = {resultModal.isOpen}
        onClose     = {closeResultModal}
        className   = "result-modal"
        aria-label  = "Operation Result"
      >
        <Title tabIndex = {-1} headingLevel = "h4" size = "lg" style = {{paddingBottom: "12px"}}>
          {resultModal.title}
        </Title>
        <div style = {{
          maxHeight: '350px',
          overflowY: 'auto',
          overflowX: 'auto',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
          backgroundColor: '#f5f5f5',
          padding: '10px',
          borderRadius: '5px'
        }}>
          {resultModal.content}
        </div>
      </Modal>
    </Flex>
  )
}

export default SystemOperations;
