import React from 'react'

import { Button, Flex, FlexItem, Icon, Modal, Dropdown, DropdownItem, DropdownList, MenuToggle, MenuToggleElement, Tooltip, TextInput, Checkbox } from '@patternfly/react-core'

import { PlusCircleIcon } from '@patternfly/react-icons'

import { useState } from 'react'

import { jolokiaService } from '@hawtio/react'
import { IJolokiaSimple } from '@hawtio/jolokia-service'
import { Logger } from '@hawtio/react'


// ********************************************************************************************************
// ********************************************************************************************************
// SystemOperations
// ********************************************************************************************************
// ********************************************************************************************************

type ArgumentInfo = {
  name: string;
  type: string;
  description: string;
};

type OptionItem = {
  signature: string;
  description: string;
  hasArgs: boolean;
  arguments?: ArgumentInfo[];
  sourceNode?: any; // The node from which this operation comes
};

type Props = {
  component?: any,
  item?: any,
  context?: any,
  isModal?: boolean,
  serverPoolName?: string,
  serverName?: string,
  groupName?: string,
  optionName?: string,
  operationsList?: OptionItem[]
};

export const SystemOperations = (props: Props): any => {
  const systemOperations = props.component?.systemOperations;
  const item = props?.item;
  const context = props.context;

  if (!context?.canAccessCounters)
    return <></>

  if (!systemOperations)
    return <></>

  const nodeTitle = cleanToFlatName(props.component?.title);
  const nodeName = removeSpaces((props?.item?.subname === null || props?.item?.subname === undefined || props?.item?.subname === " ") ? props?.item?.name : props?.item?.subname);

  const parentNameMap: Record<string, string> = {
    "ServerPoolServerInstances": "ServerPool.Server.Instance",
    "Listeners": "Listener",
    "ListenerProxies": "Listener.Proxy",
    "ServerPools": "ServerPool",
    "ServerPoolQueues": "ServerPool.Queue"
  };

  const mappedNodeTitle = parentNameMap[nodeTitle] || nodeTitle;
  const operationsList = findOpsFromOpByString(context.gtMonitorNode.children, nodeName, mappedNodeTitle);
  debugger

  function removeSpaces(str?: string) {
    if (!str) return ''; // или хвърли грешка, ако е критично
    return str.replace(/\s+/g, '');
  }
  function cleanToFlatName(str: string) {
    return str.replace(/[^a-zA-Z0-9]/g, '');
  }

  function findOpsFromOpByString(gtMonitorNodes: any[], targetName: any, nodeTitle?: string) {
    const results: OptionItem[] = [];

    function searchNode(node: any) {
      if (!node) return null;

      if (nodeTitle && node.name === nodeTitle) {
        const foundInNode = searchInNode(node, targetName);
        if (foundInNode) return foundInNode;
      }

      if (!nodeTitle && node.name === targetName) return node;
      if (!nodeTitle && node.parent?.name === targetName) return node.parent;

      if (Array.isArray(node.children)) {
        for (const child of node.children) {
          const found: any = searchNode(child);
          if (found) return found;
        }
      }

      return null;
    }

    function searchInNode(parentNode: any, targetName: string): any {
      if (!parentNode) return null;

      if (parentNode.name === targetName) return parentNode;

      if (Array.isArray(parentNode.children)) {
        for (const child of parentNode.children) {
          const found = searchInNode(child, targetName);
          if (found) return found;
        }
      }

      return null;
    }

    function simplifyType(fullType: any) {
      return fullType.split('.').pop();
    }

    let matchedNode = null;

    for (const node of gtMonitorNodes) {
      matchedNode = searchNode(node);
      if (matchedNode) break;
    }

    if (matchedNode?.mbean?.opByString) {
      const opMap = matchedNode.mbean.opByString;

      for (const [signature, data] of Object.entries(opMap)) {
        const methodName = signature.split("(")[0];
        const argString = signature.match(/\((.*)\)/)?.[1];
        const description = (data as any)?.desc || "No description available";

        if (!argString || argString.trim() === "") {
          results.push({
            signature: `${methodName}()`,
            description: description,
            hasArgs: false,
            sourceNode: matchedNode
          });
        } else {
          const argList = argString.split(",").map(arg => arg.trim());
          const shortTypes = argList.map(argType => simplifyType(argType));

          // Опитваме се да извлечем информация за аргументите от data
          const argumentsInfo: ArgumentInfo[] = [];
          const argsData = (data as any)?.args;

          if (argsData && Array.isArray(argsData)) {
            argsData.forEach((argData: any, index: number) => {
              argumentsInfo.push({
                name: argData?.name || `arg${index}`,
                type: shortTypes[index] || 'Unknown',
                description: argData?.desc || 'No description'
              });
            });
          } else {
            // Ако няма информация за аргументите, създаваме базова информация
            shortTypes.forEach((type, index) => {
              argumentsInfo.push({
                name: `arg${index}`,
                type: type,
                description: 'No description available'
              });
            });
          }

          results.push({
            signature: `${methodName}(${shortTypes.join(", ")})`,
            description: description,
            hasArgs: true,
            arguments: argumentsInfo,
            sourceNode: matchedNode
          });
        }
      }
    }

    return results;
  }

  let serverPoolName = null;
  let serverName = null;

  if (typeof item === "string") {
    serverPoolName = item.trim();
  }
  else {
    serverPoolName = (item?.name === null || item?.name === undefined) ? null : item?.name?.trim();
    serverName = (item?.subname === null || item?.subname === undefined) ? null : item?.subname?.trim();
  }

  serverPoolName = (serverPoolName === "") ? null : serverPoolName;
  serverName = (serverName === "") ? null : serverName;

  if (serverPoolName !== null && serverName !== null)
    return <SystemOperationsButtonModal serverPoolName={serverPoolName} serverName={serverName} operationsList={operationsList} />
  if (serverPoolName !== null)
    return <SystemOperationsButtonModal serverPoolName={serverPoolName} operationsList={operationsList} />
  if (serverName !== null)
    return <SystemOperationsButtonModal serverName={serverName} operationsList={operationsList} />

  return <SystemOperationsButtonModal operationsList={operationsList} />
}

// ********************************************************************************************************

export const SystemOperationsButtonModal: React.FunctionComponent<Props> = (props) => {

  const [isSystemOperationsOpen, setIsSystemOperationsOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<OptionItem | null>(null);
  const [argumentValues, setArgumentValues] = useState<Record<string, any>>({});
  const [isExecuting, setIsExecuting] = useState(false);

  // Jolokia setup
  const log = Logger.get("graphtalk-SystemOperations");
  const logPrefix = "SystemOperations";
  let jolokia: IJolokiaSimple | null = null;

  function generateAdminPath(node: any): string {
    if (!node) {
      return "com.csc.gtmonitor.*:type=Hawtio";
    }

    if (node.objectName) {
      return node.objectName;
    }

    if (!node.parent || node.parent.name === 'root' || !node.parent.parent) {
      return `com.csc.gtmonitor.*:type=${node.name}`;
    }

    return `com.csc.gtmonitor.*:type=${node.name}`;
  }

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  async function executeOperation(operation: string, args: any[] = [], sourceNode?: any) {
    setIsExecuting(true);
    await refreshJolokia();

    try {
      if (jolokia !== null) {
        const adminPath = generateAdminPath(sourceNode);

        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          log.debug(logPrefix, "executeOperation, operation: ", operation, "args: ", args, "mbean: ", mbean);

          let result;
          if (args.length === 0) {
            result = await jolokia.execute(mbean, operation);
          } else {
            result = await jolokia.execute(mbean, operation, ...args);
          }

          log.debug(logPrefix, "executeOperation, result: ", result);

          if (result !== null && result !== undefined) {
            alert(`Operation executed successfully. Result: ${result}`);
          } else {
            alert('Operation executed successfully.');
          }
        }
      }
    } catch (error) {
      log.error(logPrefix, "executeOperation, error: ", error);
      alert(`Error executing operation: ${error}`);
    } finally {
      setIsExecuting(false);
    }
  }

  function createOperationSignature(methodName: string, args: ArgumentInfo[]): string {
    if (!args || args.length === 0) {
      return methodName;
    }

    const javaTypes = args.map(arg => {
      switch (arg.type) {
        case 'String':
        case 'string':
          return 'java.lang.String';
        case 'Integer':
        case 'int':
          return 'java.lang.Integer';
        case 'Long':
        case 'long':
          return 'java.lang.Long';
        case 'Boolean':
        case 'boolean':
          return 'java.lang.Boolean';
        default:
          return 'java.lang.String';
      }
    });

    return `${methodName}(${javaTypes.join(',')})`;
  }

  function prepareArguments(args: ArgumentInfo[]): any[] {
    if (!args || args.length === 0) {
      return [];
    }

    return args.map((arg, index) => {
      const key = `arg_${index}`;
      const value = argumentValues[key];

      switch (arg.type) {
        case 'Integer':
        case 'int':
        case 'Long':
        case 'long':
          return parseInt(value) || 0;
        case 'Boolean':
        case 'boolean':
          return Boolean(value);
        case 'String':
        case 'string':
        default:
          return String(value || '');
      }
    });
  }

  async function handleExecute() {
    if (!selectedOption) return;

    const methodName = selectedOption.signature.split('(')[0];
    const args = selectedOption.arguments || [];
    const sourceNode = selectedOption.sourceNode;
    //debugger

    const operation = createOperationSignature(methodName, args);
    const operationArgs = prepareArguments(args);

    await executeOperation(operation, operationArgs, sourceNode);
  }

  const handleSystemOperationsClick = () => {
    setIsSystemOperationsOpen(true);
  }

  const handleSystemOperationsClose = () => {
    setIsSystemOperationsOpen(false);
  }

  const onDropdownSelect = (_event: React.MouseEvent<Element, MouseEvent> | undefined, value: string | number | undefined) => {
    const option = props.operationsList?.find(opt => opt.signature === value);
    setSelectedOption(option || null);
    setIsDropdownOpen(false);

    // Reset argument values when selecting new option
    const initialValues: Record<string, any> = {};
    if (option?.arguments) {
      option.arguments.forEach((arg, index) => {
        const key = `arg_${index}`;
        if (arg.type === 'Boolean' || arg.type === 'boolean') {
          initialValues[key] = false;
        } else if (arg.type === 'Integer' || arg.type === 'int' || arg.type === 'Long' || arg.type === 'long') {
          initialValues[key] = 0;
        } else {
          initialValues[key] = '';
        }
      });
    }
    setArgumentValues(initialValues);
  }

  const handleArgumentChange = (argIndex: number, value: any) => {
    const key = `arg_${argIndex}`;
    setArgumentValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const renderArgumentField = (arg: ArgumentInfo, index: number) => {
    const key = `arg_${index}`;
    const value = argumentValues[key];

    const commonStyle = {
      marginBottom: '15px',
      display: 'flex',
      flexDirection: 'column' as const
    };

    const rowStyle = {
      display: 'flex',
      alignItems: 'center',
      gap: '15px',
      marginBottom: '5px'
    };

    const labelStyle = {
      fontSize: '14px',
      fontWeight: 'normal',
      minWidth: '120px',
      flexShrink: 0
    };

    const fieldStyle = {
      flex: 1,
      maxWidth: '200px'
    };

    const numberFieldStyle = {
      width: 'auto',
      maxWidth: '120px'
    };

    const descStyle = {
      fontSize: '12px',
      color: '#666',
      fontStyle: 'italic',
      marginLeft: '135px'
    };

    if (arg.type === 'String' || arg.type === 'string') {
      return (
        <div key={key} style={commonStyle}>
          <div style={rowStyle}>
            <label style={labelStyle}>{arg.name}</label>
            <TextInput
              style={fieldStyle}
              value={value || ''}
              onChange={(_event, val) => handleArgumentChange(index, val)}
              placeholder={`Enter ${arg.name}`}
            />
          </div>
          <div style={descStyle}>{arg.description}</div>
        </div>
      );
    }

    if (arg.type === 'Integer' || arg.type === 'int' || arg.type === 'Long' || arg.type === 'long') {
      return (
        <div key={key} style={commonStyle}>
          <div style={rowStyle}>
            <label style={labelStyle}>{arg.name}</label>
            <TextInput
              style={{...numberFieldStyle, textAlign: 'left'}}
              type="number"
              value={value || 0}
              onChange={(_event, val) => handleArgumentChange(index, parseInt(val) || 0)}
              min={0}
            />
          </div>
          <div style={descStyle}>{arg.description}</div>
        </div>
      );
    }

    if (arg.type === 'Boolean' || arg.type === 'boolean') {
      return (
        <div key={key} style={commonStyle}>
          <div style={rowStyle}>
            <label style={labelStyle}>{arg.name}</label>
            <Checkbox
              id={key}
              isChecked={value || false}
              onChange={(_event, checked) => handleArgumentChange(index, checked)}
            />
          </div>
          <div style={descStyle}>{arg.description}</div>
        </div>
      );
    }

    // Default case for unknown types
    return (
      <div key={key} style={commonStyle}>
        <div style={rowStyle}>
          <label style={labelStyle}>{arg.name} ({arg.type})</label>
          <TextInput
            style={fieldStyle}
            value={value || ''}
            onChange={(_event, val) => handleArgumentChange(index, val)}
            placeholder={`Enter ${arg.name}`}
          />
        </div>
        <div style={descStyle}>{arg.description}</div>
      </div>
    );
  };

  return (
    <Flex tabIndex={-1}>
      <FlexItem tabIndex={-1} onClick={(e: any) => { e.stopPropagation(); }}>
        <Tooltip content={"Operations"}>
          <Button
            tabIndex={0}
            variant="plain"
            size="default"
            onClick={handleSystemOperationsClick}
            icon={<Icon status="info" iconSize="md" style={{ padding: "0px 0px 0px 0px" }}><PlusCircleIcon /></Icon>}
            style={{ padding: "0px 0px 0px 0px" }}
          />
        </Tooltip>
        <Modal
          isOpen={isSystemOperationsOpen}
          onClose={handleSystemOperationsClose}
          width="50%"
          height="60%"
          hasNoBodyWrapper={true}
          className="dialog"
          aria-label="SystemOperationsButtonModal"
        >
          <div style={{ padding: "20px" }}>

            <div style={{ marginTop: "20px" }}>
              <label style={{ display: "block", marginBottom: "10px", fontWeight: "bold" }}>
                Operations:
              </label>
              {props.operationsList && props.operationsList.length > 0 ? (
                <Dropdown
                  isOpen={isDropdownOpen}
                  onSelect={onDropdownSelect}
                  onOpenChange={(isOpen: boolean) => setIsDropdownOpen(isOpen)}
                  toggle={(toggleRef: React.Ref<MenuToggleElement>) => (
                    <MenuToggle
                      ref={toggleRef}
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      isExpanded={isDropdownOpen}
                      style={{ width: "300px" }}
                    >
                      {selectedOption?.signature || 'Select an option...'}
                    </MenuToggle>
                  )}
                  ouiaId="SystemOperationsDropdown"
                  shouldFocusToggleOnSelect
                >
                  <DropdownList>
                    {props.operationsList.map((option, index) => (
                      <DropdownItem key={index} value={option.signature}>
                        {option.signature}
                      </DropdownItem>
                    ))}
                  </DropdownList>
                </Dropdown>
              ) : (
                <div style={{
                  padding: "20px",
                  textAlign: "center",
                  color: "#666",
                  fontSize: "16px",
                  fontStyle: "italic",
                  backgroundColor: "#f5f5f5",
                  border: "1px solid #ddd",
                  borderRadius: "4px"
                }}>
                  This MBean has no JMX operations.
                </div>
              )}

              {selectedOption && (
                <div style={{ marginTop: "15px" }}>
                  {/* Description under dropdown with smaller font */}
                  <div style={{
                    fontSize: "12px",
                    color: "#666",
                    fontStyle: "italic",
                    marginBottom: "15px",
                    lineHeight: "1.4"
                  }}>
                    {selectedOption.description}
                  </div>

                  {!selectedOption.hasArgs && (
                    <Button
                      variant="danger"
                      onClick={handleExecute}
                      isLoading={isExecuting}
                      isDisabled={isExecuting}
                    >
                      {isExecuting ? 'Executing...' : 'Execute'}
                    </Button>
                  )}

                  {selectedOption.hasArgs && selectedOption.arguments && (
                    <div>
                      {selectedOption.arguments.map((arg, index) => renderArgumentField(arg, index))}
                      <Button
                        variant="danger"
                        onClick={handleExecute}
                        isLoading={isExecuting}
                        isDisabled={isExecuting}
                        style={{ marginTop: "10px" }}
                      >
                        {isExecuting ? 'Executing...' : 'Execute'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Modal>
      </FlexItem>
    </Flex>
  )
}

export default SystemOperations;
