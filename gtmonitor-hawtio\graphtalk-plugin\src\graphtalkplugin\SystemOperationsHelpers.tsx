// ********************************************************************************************************
// Types
// ********************************************************************************************************

export type ArgumentInfo = {
  name          : string;
  type          : string;
  description   : string;
};

export type OptionItem = {
  signature     : string;
  description   : string;
  hasArgs       : boolean;
  arguments?    : ArgumentInfo[];
  sourceNode?   : any;
};

export type Props = {
  component?        : any,
  item?             : any,
  context?          : any,
  isModal?          : boolean,
  serverPoolName?   : string,
  serverName?       : string,
  groupName?        : string,
  optionName?       : string,
  nodeName?         : string,
  operationsList?   : OptionItem[]
};

// ********************************************************************************************************
// Constants
// ********************************************************************************************************

export const parentNameMap: Record<string, string> = {
  "ServerPoolServerInstances" : "ServerPool.Server.Instance",
  "Listeners"                 : "Listener",
  "ListenerProxies"           : "Listener.Proxy",
  "ServerPools"               : "ServerPool"
};

// ********************************************************************************************************
// Helper Functions
// ********************************************************************************************************

export function removeSpaces(str?: string): string {
  if (!str) return '';
  return str.replace(/\s+/g, '');
}

export function cleanToFlatName(str: string): string {
  return str.replace(/[^a-zA-Z0-9]/g, '');
}

export function simplifyType(fullType: any): string {
  return fullType.split('.').pop();
}

export function generateAdminPath(node: any): string {
  if (!node) {
    return "com.csc.gtmonitor.*:type=Hawtio";
  }

  if (node.objectName) {
    return node.objectName;
  }

  if (!node.parent || node.parent.name === 'root' || !node.parent.parent) {
    return `com.csc.gtmonitor.*:type=${node.name}`;
  }

  return `com.csc.gtmonitor.*:type=${node.name}`;
}

export function createOperationSignature(methodName: string, args: ArgumentInfo[]): string {
  if (!args || args.length === 0) {
    return methodName;
  }

  const javaTypes = args.map(arg => {
    switch (arg.type) {
      case 'String':
      case 'string':
        return 'java.lang.String';
      case 'Integer':
      case 'int':
        return 'java.lang.Integer';
      case 'Long':
      case 'long':
        return 'java.lang.Long';
      case 'Boolean':
      case 'boolean':
        return 'java.lang.Boolean';
      default:
        return 'java.lang.String';
    }
  });

  return `${methodName}(${javaTypes.join(',')})`;
}

export function prepareArguments(args: ArgumentInfo[], argumentValues: Record<string, any>): any[] {
  return args.map((arg, index) => {
    const key = `arg_${index}`;
    const value = argumentValues[key];

    switch (arg.type) {
      case 'Integer':
      case 'int':
      case 'Long':
      case 'long':
        return parseInt(value) || 0;
      case 'Boolean':
      case 'boolean':
        return Boolean(value);
      case 'String':
      case 'string':
      default:
        return String(value || '');
    }
  });
}