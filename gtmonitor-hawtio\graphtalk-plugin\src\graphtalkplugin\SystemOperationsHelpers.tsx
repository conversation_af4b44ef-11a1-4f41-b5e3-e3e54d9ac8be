// ********************************************************************************************************
// Types and Interfaces
// ********************************************************************************************************

export type ArgumentInfo = {
  name: string;
  type: string;
  description: string;
};

export type OptionItem = {
  signature: string;
  description: string;
  hasArgs: boolean;
  arguments?: ArgumentInfo[];
  sourceNode?: any; // The node from which this operation comes
};

export type Props = {
  component?: any,
  item?: any,
  context?: any,
  isModal?: boolean,
  serverPoolName?: string,
  serverName?: string,
  groupName?: string,
  optionName?: string,
  operationsList?: OptionItem[]
};

// ********************************************************************************************************
// Constants
// ********************************************************************************************************

export const parentNameMap: Record<string, string> = {
  "ServerPoolServerInstances": "ServerPool.Server.Instance",
  "Listeners": "Listener",
  "ListenerProxies": "Listener.Proxy",
  "ServerPools": "ServerPool"
};

// ********************************************************************************************************
// Helper Functions
// ********************************************************************************************************

export function removeSpaces(str?: string): string {
  if (!str) return '';
  return str.replace(/\s+/g, '');
}

export function cleanToFlatName(str: string): string {
  return str.replace(/[^a-zA-Z0-9]/g, '');
}

export function simplifyType(fullType: any): string {
  return fullType.split('.').pop();
}

export function generateAdminPath(node: any): string {
  if (!node) {
    return "com.csc.gtmonitor.*:type=Hawtio";
  }

  if (node.objectName) {
    return node.objectName;
  }

  if (!node.parent || node.parent.name === 'root' || !node.parent.parent) {
    return `com.csc.gtmonitor.*:type=${node.name}`;
  }

  return `com.csc.gtmonitor.*:type=${node.name}`;
}

export function createOperationSignature(methodName: string, args: ArgumentInfo[]): string {
  if (!args || args.length === 0) {
    return methodName;
  }

  const javaTypes = args.map(arg => {
    switch (arg.type) {
      case 'String':
      case 'string':
        return 'java.lang.String';
      case 'Integer':
      case 'int':
        return 'java.lang.Integer';
      case 'Long':
      case 'long':
        return 'java.lang.Long';
      case 'Boolean':
      case 'boolean':
        return 'java.lang.Boolean';
      default:
        return 'java.lang.String';
    }
  });

  return `${methodName}(${javaTypes.join(',')})`;
}

export function prepareArguments(args: ArgumentInfo[], argumentValues: Record<string, any>): any[] {
  return args.map((arg, index) => {
    const key = `arg_${index}`;
    const value = argumentValues[key];

    switch (arg.type) {
      case 'Integer':
      case 'int':
      case 'Long':
      case 'long':
        return parseInt(value) || 0;
      case 'Boolean':
      case 'boolean':
        return Boolean(value);
      case 'String':
      case 'string':
      default:
        return String(value || '');
    }
  });
}

// ********************************************************************************************************
// Operations Search Functions
// ********************************************************************************************************

export function findOpsFromOpByString(gtMonitorNodes: any[], targetName: any, nodeTitle?: string): OptionItem[] {
  const results: OptionItem[] = [];

  function searchNode(node: any) {
    if (!node) return null;

    if (nodeTitle && node.name === nodeTitle) {
      const foundInNode = searchInNode(node, targetName);
      if (foundInNode) return foundInNode;
    }

    if (!nodeTitle && node.name === targetName) return node;
    if (!nodeTitle && node.parent?.name === targetName) return node.parent;

    if (Array.isArray(node.children)) {
      for (const child of node.children) {
        const found: any = searchNode(child);
        if (found) return found;
      }
    }

    return null;
  }

  function searchInNode(parentNode: any, targetName: string): any {
    if (!parentNode) return null;

    if (parentNode.name === targetName) return parentNode;

    if (Array.isArray(parentNode.children)) {
      for (const child of parentNode.children) {
        const found = searchInNode(child, targetName);
        if (found) return found;
      }
    }

    return null;
  }

  let matchedNode = null;

  for (const node of gtMonitorNodes) {
    matchedNode = searchNode(node);
    if (matchedNode) break;
  }

  if (matchedNode?.mbean?.opByString) {
    const opMap = matchedNode.mbean.opByString;

    for (const [signature, data] of Object.entries(opMap)) {
      const methodName = signature.split("(")[0];
      const argString = signature.match(/\((.*)\)/)?.[1];
      const description = (data as any)?.desc || "No description available";

      if (!argString || argString.trim() === "") {
        results.push({
          signature: `${methodName}()`,
          description: description,
          hasArgs: false,
          sourceNode: matchedNode
        });
      } else {
        const argList = argString.split(",").map(arg => arg.trim());
        const shortTypes = argList.map(argType => simplifyType(argType));

        // Опитваме се да извлечем информация за аргументите от data
        const argumentsInfo: ArgumentInfo[] = [];
        const argsData = (data as any)?.args;

        if (argsData && Array.isArray(argsData)) {
          argsData.forEach((argData: any, index: number) => {
            argumentsInfo.push({
              name: argData?.name || `arg${index}`,
              type: shortTypes[index] || 'Unknown',
              description: argData?.desc || 'No description'
            });
          });
        } else {
          // Ако няма информация за аргументите, създаваме базова информация
          shortTypes.forEach((type, index) => {
            argumentsInfo.push({
              name: `arg${index}`,
              type: type,
              description: 'No description available'
            });
          });
        }

        results.push({
          signature: `${methodName}(${shortTypes.join(", ")})`,
          description: description,
          hasArgs: true,
          arguments: argumentsInfo,
          sourceNode: matchedNode
        });
      }
    }
  }

  return results;
}
