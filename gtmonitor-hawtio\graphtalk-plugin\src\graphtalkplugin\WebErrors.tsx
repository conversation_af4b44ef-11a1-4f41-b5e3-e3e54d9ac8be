
import * as JulianDateTime from './JulianDateTime'

import { jmx, Logger, jolokiaService } from '@hawtio/react'

import { IJolokiaSimple } from '@jolokia.js/simple'

import {
  Badge,
  Bullseye,
  Button,
  DatePicker, TimePicker,
  EmptyState, EmptyStateVariant, EmptyStateHeader, EmptyStateBody, EmptyStateIcon,
  Divider,
  Drawer, DrawerPanelContent, DrawerContent, DrawerContentBody,
  ExpandableSection,
  Grid, GridItem,
  Icon,
  Modal, ModalVariant,
  Pagination,
  Split, SplitItem,
  Tabs, Tab, TabTitleText,
  Text, TextContent, TextInput, TextVariants,
  Title,
  Tooltip,
  isValidDate, Dropdown, DropdownItem, DropdownList, MenuToggle, MenuToggleElement
} from '@patternfly/react-core'

import {
  Table,
  Thead,
  Tr,
  Th, ThProps,
  Tbody,
  Td,
  InnerScrollContainer
} from '@patternfly/react-table';

import {DownloadIcon, EraserIcon, SearchIcon, TrashIcon} from '@patternfly/react-icons/dist/esm/icons'

import React, { useEffect, useRef, useState } from 'react'

import GraphTalkComponent, { GraphTalkComponentDiv } from './GraphTalkComponent';

import { getContext } from './Context';

import './GlobalPlugins.css';

import {webErrorsItemsMap, formNumbersList, getDeltaValue} from "./WebErrorsHelpers"


// ********************************************************************************************************
// ********************************************************************************************************
// Global
// ********************************************************************************************************
// ********************************************************************************************************

const log = Logger.get("graphtalk-weberrors");

const logPrefix             = "** WebErrors ** ";
 
//log.setLevel(Logger.INFO);
  
log.info(logPrefix, "LOG LEVEL IS:", log.getLevel().name);


// ********************************************************************************************************
// ********************************************************************************************************
// Web Errors
// ********************************************************************************************************
// ********************************************************************************************************

const adminPath = "com.csc.gtmonitor.*:type=Hawtio";

const WebErrors : React.FunctionComponent = () => {

  type WebErrorsType = {
    retrievedData       : any,
    computedData        : any
  }
 
  // ********************************************************************************************************

  const [webErrors, setWebErrors] = useState<WebErrorsType> ({
    retrievedData       : false,
    computedData        : []
  })
  
  const [allWebErrorsCount, setAllWebErrorsCount] = useState<WebErrorsType>({
    retrievedData       : false,
    computedData        : []
  })
 
  // ********************************************************************************************************
 
  const activeTabKey = useRef<string | number>(0);

  // ********************************************************************************************************

  const webErrorsPanelContentSize = useRef<any>("30%");

  // ********************************************************************************************************
  
  const defaultSortIndex      = 0;
  const defaultSortDirection  = 'desc';

  const activeSortIndex = useRef<any>(defaultSortIndex);
  const activeSortDirection = useRef<any>(defaultSortDirection);


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Jolokia
  // ********************************************************************************************************
  // ********************************************************************************************************

  let jolokia : IJolokiaSimple | null = null;

  async function refreshJolokia() {
    jolokia = null;
    try {
      jolokia = await jolokiaService.getJolokia();
      log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
    }
    catch (error) {
      log.error(logPrefix, "retrieveJolokia, error: ", error);
    }
  }

  
  // ********************************************************************************************************
  // ********************************************************************************************************
  // Search Criterias
  // ********************************************************************************************************
  // ********************************************************************************************************

  const searchExpanded   = useRef<boolean>(true);
  const setSearchExpanded = (newSearchExpanded : boolean) => { searchExpanded.current = newSearchExpanded; }
  const isSearchExpanded = () => { return searchExpanded.current }
  const toggleSearchExpanded = () => { searchExpanded.current = !searchExpanded.current; } 

  const defaultSearchStart     = 0;
  const defaultSearchNum       = 50;
  const defaultSearchCount     = 1;
  const defaultSearchNo        = null;
  const defaultSearchUser      = null;
  const defaultSearchDateMin   = null;
  const defaultSearchTimeMin   = null;
  const defaultSearchDateMax   = null;
  const defaultSearchTimeMax   = null;
  
  const searchStart           = useRef<number|null>(null);
  const searchNum             = useRef<number|null>(defaultSearchNum);
  const searchCount           = useRef<number|null>(null);
  const searchNo              = useRef<number|null>(null);
  const searchUser            = useRef<string|null>(null);
  const searchDateMin         = useRef<Date|null>(null);
  const searchTimeMin         = useRef<Date|null>(null);
  const searchDateMax         = useRef<Date|null>(null);
  const searchTimeMax         = useRef<Date|null>(null);

  const searchReset = () => {
    searchStart.current   = null;
    searchNum.current     = defaultSearchNum;
    searchCount.current   = null;
    searchNo.current      = null;
    searchUser.current    = null;
    searchDateMin.current = null;
    searchTimeMin.current = null;
    searchDateMax.current = null;
    searchTimeMax.current = null;
  }

  
  // ********************************************************************************************************
  // ********************************************************************************************************
  // Pagination Variables
  // ********************************************************************************************************
  // ********************************************************************************************************

  const defaultPerPage = 25;

  const perPage = useRef<number>(defaultPerPage);

  const setPerPage = (newPerPage : number) => {
    perPage.current = newPerPage;
  }

  const getPerPage = () : number => {
    return perPage.current;
  }

  // ********************************************************************************************************
  // ********************************************************************************************************
  // Retrieve Data
  // ********************************************************************************************************
  // ********************************************************************************************************

  const retrieveWebErrorsSignature = "retrieveWebErrors(java.lang.String)";

  async function retrieveWebErrors(webErrors : any) {
    const args : any = {"sort" : "-num"};

    const argStart   = (searchStart.current   === null) ? defaultSearchStart    : searchStart.current;
    const argNum     = (searchNum.current     === null) ? defaultSearchNum      : searchNum.current;
    const argCount   = (searchCount.current   === null) ? defaultSearchCount    : searchCount.current;
    const argNo      = (searchNo.current      === null) ? defaultSearchNo       : searchNo.current;
    const argUser    = (searchUser.current    === null) ? defaultSearchUser     : searchUser.current;
    const argDateMin = (searchDateMin.current === null) ? defaultSearchDateMin  : JulianDateTime.dateToJulian(searchDateMin.current);
    const argTimeMin = (searchTimeMin.current === null) ? defaultSearchTimeMin  : JulianDateTime.timeToJulian(searchTimeMin.current);
    const argDateMax = (searchDateMax.current === null) ? defaultSearchDateMax  : JulianDateTime.dateToJulian(searchDateMax.current);
    const argTimeMax = (searchTimeMax.current === null) ? defaultSearchTimeMax  : JulianDateTime.timeToJulian(searchTimeMax.current);

    if (argStart   !== null && argStart       > 0) args["_start"]   = argStart;     else args["_start"] = 0;
    if (argNum     !== null && argNum         > 0) args["_num"]     = argNum;
    if (argCount   !== null && argCount       > 0) args["_count"]   = argCount;
    if (argNo      !== null && argNo          > 0) args["num"]      = argNo;
    if (argUser    !== null && argUser.length > 0) args["userId"]   = argUser;
    if (argDateMin !== null && argDateMin     > 0) args["date_min"] = argDateMin;
    if (argTimeMin !== null && argTimeMin     > 0) args["time_min"] = argTimeMin;
    if (argDateMax !== null && argDateMax     > 0) args["date_max"] = argDateMax;
    if (argTimeMax !== null && argTimeMax     > 0) args["time_max"] = argTimeMax;

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveWebErrorsSignature;
          log.debug(logPrefix, "retrieveWebErrors, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrieveWebErrors, result: ", json);
          webErrors.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrieveWebErrors, exception: ", e);
      }
    }
  }

  async function retrieveAllWebErrorsCount(allWebErrorsCount : any) {
    const args : any = { "_num" : 0};

    if (jolokia !== null) {
      try {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = retrieveWebErrorsSignature;
          log.debug(logPrefix, "retrievWebErrors, execute : ", JSON.stringify(args));
          const result = await jolokia.execute(mbean, operation, JSON.stringify(args));
          const json : any = (result === null) ? null : JSON.parse(result.toString());
          log.debug(logPrefix, "retrievWebErrors, result: ", json);
          allWebErrorsCount.retrievedData = json;
        }
      }
      catch (e) {
        log.debug(logPrefix, "retrievWebErrors, exception: ", e);
      }
    }
  }


  // ********************************************************************************************************
  // ********************************************************************************************************
  // Compute Data
  // ********************************************************************************************************
  // ********************************************************************************************************

 
  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying Web Errors ****************************************************************************");

  // ********************************************************************************************************

  const WebErrorsItems = (props : any) => {
  
    const stickyColumn = -1;   // no sticky column, else put the column index like 0 for the first one

    const [activeSortRefresh, setActiveSortRefresh] = useState<boolean>(false);
    const toggleActiveSortRefresh = () => { setActiveSortRefresh(!activeSortRefresh); }

    const setActiveSortIndex = (sortIndex : any) => { activeSortIndex.current = sortIndex;  toggleActiveSortRefresh(); }
    const getActiveSortIndex = () : any => { return activeSortIndex.current; }

    const setActiveSortDirection = (sortDirection : any) => { activeSortDirection.current = sortDirection; toggleActiveSortRefresh(); }
    const getActiveSortDirection = () : any => { return activeSortDirection.current; }

    let webErrorsItems : {[key : number] : any}[] = webErrors.computedData.gtweberrors?.items;
    if (webErrorsItems === null || webErrorsItems === undefined) {
      webErrorsItems = [];
    }
  
    let sortedWebErrorsItems = webErrorsItems;
    if (activeSortIndex !== null) {
      sortedWebErrorsItems = webErrorsItems.sort((a, b) => {
        const column = webErrorsItemsMap[getActiveSortIndex()];
        const key     : any = column.key;
        const aValue  : any = a[key];
        const bValue  : any = b[key];
        if (typeof aValue === 'number') {
          // Numeric sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as number) - (bValue as number);
          }
          return (bValue as number) - (aValue as number);
        }
        else {
          // String sort
          if (getActiveSortDirection() === 'asc') {
            return (aValue as string).localeCompare(bValue as string);
          }
          return (bValue as string).localeCompare(aValue as string);
        }
      });
    }
  
    const getSortParams = (columnIndex : number) : ThProps['sort'] => ({
      sortBy : {
        index             : getActiveSortIndex(),
        direction         : getActiveSortDirection(),
        defaultDirection  : 'asc' // starting sort direction when first sorting a column. Defaults to 'asc'
      },
      onSort : (_event, index, direction) => {
        setActiveSortIndex(index);
        setActiveSortDirection(direction);
      },
      columnIndex
    });

    // ********************************************************************************************************

    const isWebErrorItemSelectable = (webErrorItem : any) => webErrorItem.num !== '0'; // Arbitrary logic for this example

    const selectableWebErrorsItems = webErrorsItems.filter(isWebErrorItemSelectable);

    const [selectedWebErrorsItemsNums, setSelectedWebErrorsItemsNums] = useState<string[]>([]);

    const setWebErrorItemSelected = (webErrorItem : any, isSelecting = true) =>
      setSelectedWebErrorsItemsNums((prevSelected) => {
        const otherSelectedWebErrorsItemsNums = prevSelected.filter((w) => w !== webErrorItem.num);
        return isSelecting && isWebErrorItemSelectable(webErrorItem) ? [...otherSelectedWebErrorsItemsNums, webErrorItem.num]
                                                                     : otherSelectedWebErrorsItemsNums;
      });

    const selectAllWebErrorsItems = (isSelecting = true) => 
      setSelectedWebErrorsItemsNums(isSelecting ? selectableWebErrorsItems.map((webErrorItem : any) => webErrorItem.num) : []);

    const areAllWebErrorsItemsSelected = () => { return (selectedWebErrorsItemsNums.length === selectableWebErrorsItems.length) };

    const areSeveralWebErrorsItemsSelected = () => { return (selectedWebErrorsItemsNums.length > 0) };

    const isWebErrorItemSelected = (webErrorItem : any) => selectedWebErrorsItemsNums.includes(webErrorItem.num);

    const [recentSelectedRowIndex, setRecentSelectedRowIndex] = useState<number | null>(null);

    // ********************************************************************************************************

    const shiftKey = useRef(false);
    const setShiftKey = (state : boolean) => { shiftKey.current = state; }
    const isShiftKey = () : boolean => { return shiftKey.current; }

    const controlKey = useRef(false);
    const setControlKey = (state : boolean) => { controlKey.current = state; }
    const isControlKey = () : boolean => { return controlKey.current; }

    // ********************************************************************************************************

    const onSelectWebErrorItem = (webErrorItem : any, rowIndex : number, isSelecting : boolean) => {
      // If the user does control + selecting the checkboxes, then selected checkbox should be added to existing ones
      // If the user does shift + selecting the checkboxes, then all intermediate checkboxes should be selected
      if (isShiftKey() && recentSelectedRowIndex !== null) {
        const numberSelected = rowIndex - recentSelectedRowIndex;
        const intermediateIndexes =
          numberSelected > 0
            ? Array.from(new Array(numberSelected + 1), (_x, i) => i + recentSelectedRowIndex)
            : Array.from(new Array(Math.abs(numberSelected) + 1), (_x, i) => i + rowIndex);
        intermediateIndexes.forEach((index) => setWebErrorItemSelected(webErrorsItems[index], isSelecting));
      }
      else
      if (isControlKey())
        setWebErrorItemSelected(webErrorItem, isSelecting);
      else
        if (isWebErrorItemSelectable(webErrorItem) && !isWebErrorItemSelected(webErrorItem))
          setSelectedWebErrorsItemsNums([webErrorItem.num]);
        else
          setSelectedWebErrorsItemsNums([]);

      setRecentSelectedRowIndex(rowIndex);
    };

    // ********************************************************************************************************
  
    useEffect(() => {
      const onKeyDown = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(true);
        if (e.key === 'Control')
          setControlKey(true);
      };

      const onKeyUp = (e : KeyboardEvent) => {
        if (e.key === 'Shift')
          setShiftKey(false);
        if (e.key === 'Control')
          setControlKey(false);
      };
  
      document.addEventListener('keydown', onKeyDown);
      document.addEventListener('keyup',   onKeyUp);
  
      return () => {
        document.removeEventListener('keydown', onKeyDown);
        document.removeEventListener('keyup',   onKeyUp);
      };
    }, []);
  
    // ********************************************************************************************************

    // Selected rows are tracked by the web errors num field from each row.
    // This is to prevent state from being based on the row order index which is subject to sorting.

    // ********************************************************************************************************

    const WebErrorDetails = () => {
      const [webErrorDetails, setWebErrorDetails] = useState<any>(null);
  
      // ********************************************************************************************************

      async function retrieveWebErrorDetails(webErrorItem : any) : Promise<any> {
        log.debug(logPrefix,  "retrieveWebErrorDetails : " +  webErrorItem?.num + " / " + webErrorItem)

        const webErrorNum = webErrorItem?.num;

        let jolokia = null;
        try {
          jolokia = await jolokiaService.getJolokia();
          log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
        }
        catch (error) {
          log.error(logPrefix, "retrieveJolokia, error: ", error);
        }
    
        let json : any = null;
        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveWebError(java.lang.Integer)";
            const result = await jolokia.execute(mbean, operation, webErrorNum);
            log.debug(logPrefix, "retrieveWebErrorDetails, result: ", result);
            json = (result === undefined || result === null) ? null : JSON.parse(result.toString());
          }
        }

        log.debug(logPrefix, "retrieveWebErrorDetails, json: ", json);
        
        let gtweberror  = (json === undefined || json === null) ? null : json.gtweberror;

        if (gtweberror === null) {
          log.debug(logPrefix, "retrieveWebErrorDetails, cannot retrieve web error details for: ", webErrorNum);
          gtweberror = -1;
        }
        else
          log.debug(logPrefix, "retrieveWebErrorDetails, web error details retrieved for: ", webErrorNum);
    
        setWebErrorDetails(gtweberror);
      }
  
      // ********************************************************************************************************
    
      useEffect(() => {
        const nbSelected = selectedWebErrorsItemsNums.length;

        if (nbSelected === 1) {
          const webErrorItem : any = webErrorsItems.find((item : any) => (item.num === selectedWebErrorsItemsNums[0]));
          retrieveWebErrorDetails(webErrorItem)
          .then  (()    => {
            log.debug(logPrefix, "retrieveWebErrorDetails OK");
          })
          .catch (error => {
            log.debug(logPrefix, "retrieveWebErrorDetails exception: ", error);
            setWebErrorDetails(null);
          })
        }
        else
          setWebErrorDetails(null);
      }, [selectedWebErrorsItemsNums]);
  
      // ********************************************************************************************************

      const WebErrorMsgBody = (props : any) => {
        const webErrorMsgBody = (props?.data) ? props.data : [];
        
        return (
          <Tr tabIndex = {-1}>
            <Td tabIndex = {-1}>
            {
              webErrorMsgBody.map((webErrorMsgBodyItem : any, webErrorMsgBodyItemIndex : number) => 
                <p tabIndex = {-1} className = "font-size-13" key = {webErrorMsgBodyItemIndex}>{webErrorMsgBodyItem}</p>)
            }
            </Td>
          </Tr>
        )
      }
  
      // ********************************************************************************************************

      const WebErrorMsg = (props : any) => {
        const webErrorMsg         = props.webErrorMsg;

        const webErrorMsgHeader   = webErrorMsg[0];
        const webErrorMsgBody     = webErrorMsg[1];

        return (
          <>
            <Tr tabIndex = {-1}>
              <Td tabIndex = {-1}>
                <p tabIndex = {-1} className = "font-size-13" style = {{fontWeight : 'bold'}}>{webErrorMsgHeader}</p>
              </Td>
            </Tr>
            <WebErrorMsgBody data = {webErrorMsgBody}/>
          </>
        )
      }
  
      // ********************************************************************************************************

      const WebErrorBody = () => {
        const nbSelected = selectedWebErrorsItemsNums.length;

        // display the error details when only one error is selected
        if (nbSelected !== 1 || webErrorDetails === undefined || webErrorDetails === null)
            return <></>
  
        if (webErrorDetails === -1)
          return (
            <Tr tabIndex = {-1}>
              <Td tabIndex = {-1}>
                <p tabIndex = {-1} style = {{color : 'red'}}>Cannot retrieve detail for the error</p>
              </Td>
            </Tr>
          )

        const webErrorBody = (webErrorDetails?.detail) ? webErrorDetails.detail : null;
        const webErrorBodyLength = webErrorBody.length;

        return (
          <>
          {
            webErrorBody.map((_ : any, webErrorMsgIndex : number) => {
              // the error body messages must be displayed in reverse order ...
              const webErrorMsg = webErrorBody[webErrorBodyLength - webErrorMsgIndex - 1]
              return (
                <WebErrorMsg key = {webErrorMsgIndex} webErrorMsg = {webErrorMsg}/>
              )
            })
          }
          </>
        )
      }

      // ********************************************************************************************************

      const WebErrorTabBody = (props : any) => {
        const nbSelected = selectedWebErrorsItemsNums.length;

        return (
          <GraphTalkComponentDiv delta = {-55}>
            <InnerScrollContainer tabIndex = {-1}>
              <Table tabIndex = {-1} className = "custom-compact-table" isStriped aria-label = "Error Details table" variant = "compact" borders = {false}>
                <Tbody tabIndex = {-1}>
                {
                  (nbSelected == 1 && webErrorDetails !== undefined && webErrorDetails !== null) ?
                    props.children
                  :
                  <Tr tabIndex = {-1}>
                    <Td tabIndex = {-1}>
                      <p tabIndex = {-1} style = {{fontWeight : 'normal'}}>{(nbSelected === 0) ? "No Selection" : "Multiple Selection"}</p>
                    </Td>
                    </Tr>
                }
                </Tbody>
              </Table>
            </InnerScrollContainer>
          </GraphTalkComponentDiv>
        )
      }

      // ********************************************************************************************************

      const WebErrorProperty = (props : any) => {
        const altValue = props?.altvalue ? <>({props.altvalue})</> : "";

        return (
          <Tr tabIndex = {-1}>
            <Td tabIndex = {-1} modifier = "fitContent"><p tabIndex = {-1} className = "font-size-13" style = {{fontWeight : 'bold'}}>{props.name}</p></Td>
            <Td tabIndex = {-1} modifier = "fitContent"><p tabIndex = {-1} className = "font-size-13" style = {{fontWeight : 'normal'}}>{props?.value}</p></Td>
            <Td tabIndex = {-1}><p tabIndex = {-1} style = {{fontWeight : 'normal'}}>{altValue}</p></Td>
          </Tr>
        )
      }

      // ********************************************************************************************************

      const [refreshTabs, setRefreshTabs] = useState<boolean>(true);

      const handleTabClick = (
        event     : React.MouseEvent<any> | React.KeyboardEvent | MouseEvent,
        tabIndex  : string | number
      ) => {
        activeTabKey.current = tabIndex;
        setRefreshTabs(!refreshTabs);
      };

      return (
        <Tabs tabIndex = {-1} activeKey = {activeTabKey.current} onSelect = {handleTabClick} isVertical = {false} isBox = {false} role = "region">
          <Tab eventKey = {0} title = {<TabTitleText tabIndex = {-1} className = "font-size-14" aria-label = "vertical" role = "region">Error Details</TabTitleText>}>
            <WebErrorTabBody>
              <WebErrorBody/>
            </WebErrorTabBody>
          </Tab>
          <Tab eventKey = {1} title = {<TabTitleText tabIndex = {-1} className = "font-size-14" aria-label = "vertical" role = "region">Related Request</TabTitleText>}>
            <WebErrorTabBody>
              <Tr tabIndex = {-1}>
                <Td tabIndex = {-1}>
                  <p tabIndex = {-1} style = {{fontWeight : 'bold'}}>Raw content of the request that triggered this error</p>
                </Td>
              </Tr>
              <Tr tabIndex = {-1}>
                <Td tabIndex = {-1}>
                  <p tabIndex = {-1}>{(webErrorDetails?.request) ? webErrorDetails.request : null}</p>
                </Td>
              </Tr>
            </WebErrorTabBody>
          </Tab>
          <Tab eventKey = {2} title = {<TabTitleText tabIndex = {-1} className = "font-size-14" aria-label = "vertical" role = "region">Error Properties</TabTitleText>}>
            <WebErrorTabBody>
              {webErrorsItemsMap.map((item) => {
                const rawValue = webErrorDetails?.[item.key] ?? null;
                let formattedValue = rawValue;
                let altvalue = undefined;

                if (item.type === "julianDate" && rawValue) {
                  formattedValue = JulianDateTime.formatJulianAsDate(rawValue);
                  altvalue = rawValue;
                } else if (item.type === "julianTime" && rawValue) {
                  formattedValue = JulianDateTime.formatJulianAsTime(rawValue);
                  altvalue = rawValue;
                }

                return (
                  <WebErrorProperty
                    key      = {item.key}
                    name     = {item.header}
                    value    = {formattedValue}
                    altvalue = {altvalue}
                  />
                );
              })}
            </WebErrorTabBody>
          </Tab>
        </Tabs>
      )
    }
  
    // ********************************************************************************************************

    const displayValue = (value : any, type : string) : string => {
      switch (type) {
        case "int"          : return value.toString();
        case "string"       : return value;
        case "julianTime"   : return JulianDateTime.formatJulianAsTime(value);
        case "julianDate"   : return JulianDateTime.formatJulianAsDate(value);
        default             : return value;  // let it be automatically converted ...
      }
    }

    const getDynamicFontPadding = (key : string) => {
      switch (key) {
        case "elapsed" : return "0 4em 0 0";
        default        : return "0 0.5em";
      }
    }

    // ********************************************************************************************************

    const WebErrorItem = (props : any) => {
      const webErrorItem      = props.webErrorItem;
      const rowIndex          = props.rowIndex;

      return (
        <Tr tabIndex = {-1} style = {{cursor : 'pointer', fontFamily : 'monospace'}} onClick = {() => onSelectWebErrorItem(webErrorItem, rowIndex, true)}>
        {
          (stickyColumn >= 0) ?
            <Td
              tabIndex  = {-1}
              className = "custom-compact-table_padding"
              select    = {{
                rowIndex    : rowIndex,
                onSelect    : (_event, isSelecting) => onSelectWebErrorItem(webErrorItem, rowIndex, isSelecting),
                isSelected  : isWebErrorItemSelected(webErrorItem),
                isDisabled  : !isWebErrorItemSelectable(webErrorItem)
              }}
            />
          :
            <Td
              tabIndex  = {-1}
              className = "custom-compact-table_padding"
              select    = {{
                rowIndex    : rowIndex,
                onSelect    : (_event, isSelecting) => onSelectWebErrorItem(webErrorItem, rowIndex, isSelecting),
                isSelected  : isWebErrorItemSelected(webErrorItem),
                isDisabled  : !isWebErrorItemSelectable(webErrorItem)
              }}
            />
        }
        {
         webErrorsItemsMap.map((column : any, columnIndex : number) =>
            (columnIndex === stickyColumn) ?
              <Th
                tabIndex          = {-1}
                className         = "custom-compact-table_padding"
                key               = {column.key}
                isStickyColumn
                stickyMinWidth    = "auto"
                stickyLeftOffset  = "0px"
                hasRightBorder    = {true}
                modifier          = "truncate"
                dataLabel         = {column.key}
                style             = {{fontSize : 'inherit', textAlign : column.align, padding : column.padding}}
              >
                {displayValue(webErrorItem[column.key], column.type)}
              </Th>
            :
              <Td
                tabIndex          = {-1}
                className         = "custom-compact-table_padding"
                key               = {column.key}
                modifier          = "truncate"
                dataLabel         = {column.key}
                style             = {{fontSize : 'inherit', textAlign : column.align, padding : column.padding}}
              >
                {displayValue(webErrorItem[column.key], column.type)}
              </Td>
        )
        }
        </Tr>
      )
    }
    
    // ********************************************************************************************************

    const handleOnPanelContentResize = (_event : MouseEvent | TouchEvent | React.KeyboardEvent, newWidth : number, id : string) => {
      webErrorsPanelContentSize.current = newWidth + "px";
    };

    const webErrorsPanelContent = (
      <DrawerPanelContent
        isResizable
        onResize      = {handleOnPanelContentResize}
        id            = "drawerPanelContent"
        defaultSize   = {webErrorsPanelContentSize.current}
        minSize       = "10%"
        maxSize       = "50%"
      >
        <DrawerContentBody tabIndex = {-1}>
          <WebErrorDetails/>
        </DrawerContentBody>
      </DrawerPanelContent>
    );

    // ********************************************************************************************************

    const WebErrorDate = (props : any) => {
      const ddMMyyyyFormat = (date : Date) =>
        (date === undefined || date === null) ? "" : `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;

      const datePickerRef = useRef<any>();

      const dateParse = (value : string) : any => {
        if (value === undefined || value === null) return null;
        const splittedDate = value.split('/').map(Number);
        if (splittedDate.length === 3) {
          const date = new Date(Date.UTC(splittedDate[2], splittedDate[1] - 1, splittedDate[0]));
          date.setHours (0, 0, 0);
          if (isValidDate(date) && value === ddMMyyyyFormat(date)) {
            return date
          }
        }
        return null;
      }

      const changeDate = (value : string, date? : Date | undefined) => {
        if (value !== undefined && value !== null && value.length > 0 && date !== undefined && isValidDate(date) && ddMMyyyyFormat(date) === value)
          props.dateHandler(date);
        else
          props.dateHandler(null);
      }

      const onDateChange = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        if (datePickerRef.current?.isCalendarOpen)
          changeDate(value, date);
      }

      const onDateBlur = (_event : React.FormEvent<HTMLInputElement>, value : string, date? : Date | undefined) => {
        changeDate(value, date);
      }

      return (
        <DatePicker
          ref         = {datePickerRef}
          id          = {props.id}
          dateFormat  = {ddMMyyyyFormat}
          placeholder = "DD/MM/YYYY"
          dateParse   = {(value : string) => dateParse(value)}
          value       = {(props.date === undefined || props.Date === null) ? undefined : ddMMyyyyFormat(props.date)}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : {fontSize : "12px", padding : "10px 0px 0px 10px"}}}
          onChange    = {onDateChange}
          onBlur      = {onDateBlur}
        />
      )
    }

    // ********************************************************************************************************

    const WebErrorTime = (props : any) => {
      const onTimeChange = (event : React.FormEvent<HTMLInputElement>, time : string, hour? : number | undefined, minute? : number | undefined, seconds? : number | undefined, isValid? : boolean | undefined) => {
        if (isValid && time !== null && time.length > 0 && hour != null && minute !== null && seconds !== null) {
          const dateTime = new Date(0);
          dateTime.setHours (hour ? hour : 0, minute ? minute : 0, seconds ? seconds : 0);
          props.timeHandler(dateTime);
        }
        else
          props.timeHandler(null);
      }

      return (
        <TimePicker
          id          = {props.id}
          is24Hour
          includeSeconds
          placeholder = 'HH:mm:ss'
          stepMinutes = {15}
          time        = {(props.time === undefined || props.time === null) ? undefined : props.time}
          onChange    = {onTimeChange}
          style       = {{fontSize : "12px"}}
          inputProps  = {{style : {fontSize : "12px", padding : "5px 0px 0px 10px"}}}
        />
      )
    }

    // ********************************************************************************************************
    const [heightDelta, setHeightDelta] = useState<number>(
      getDeltaValue(isSearchExpanded())
    );
    
    const refreshHeightDelta = () => {
      setHeightDelta(getDeltaValue(isSearchExpanded()));
    };

    // ********************************************************************************************************

    const WebErrorsSearch = () => {
      const [searchRefresh, setSearchRefresh] = useState<boolean>(false);      
      const [formRestartDate, setFormRestartDate] = useState<any>(null);
      const forceSearchRefresh = () => { setSearchRefresh(!searchRefresh) }
      const [isNumbersDropdownOpen, setIsNumbersDropdownOpen] = React.useState(false);
  
      const [formSearchNo,      setFormSearchNo     ] = useState<number|null>((searchNo.current      !== undefined) ? searchNo.current      : defaultSearchNo);
      const [formSearchNum,     setFormSearchNum    ] = useState<number|null>((searchNum.current     !== undefined) ? searchNum.current     : null);
      const [formSearchUser,    setFormSearchUser   ] = useState<string|null>((searchUser.current    !== undefined) ? searchUser.current    : null);
      const [formSearchDateMin, setFormSearchDateMin] = useState<Date  |null>((searchDateMin.current !== undefined) ? searchDateMin.current : null);
      const [formSearchTimeMin, setFormSearchTimeMin] = useState<Date  |null>((searchTimeMin.current !== undefined) ? searchTimeMin.current : null);
      const [formSearchDateMax, setFormSearchDateMax] = useState<Date  |null>((searchDateMax.current !== undefined) ? searchDateMax.current : null);
      const [formSearchTimeMax, setFormSearchTimeMax] = useState<Date  |null>((searchTimeMax.current !== undefined) ? searchTimeMax.current : null);

      const handleFormSearchDateMin = (date : any) => {
        setFormSearchDateMin((date !== undefined && date !== null) ? date : defaultSearchDateMin);
      }

      const handleFormSearchTimeMin = (time : any) => {
        setFormSearchTimeMin((time !== undefined && time !== null) ? time : defaultSearchTimeMin);
      }

      const handleFormSearchDateMax = (date : any) => {
        setFormSearchDateMax((date !== undefined && date !== null) ? date : defaultSearchDateMax);
      }

      const handleFormSearchTimeMax = (time : any) => {
        setFormSearchTimeMax((time !== undefined && time !== null) ? time : defaultSearchTimeMax);
      }

      const formSearchReset = () => {
        setFormSearchNum(defaultSearchNum);
        setFormSearchNo(defaultSearchNo);
        setFormSearchUser(defaultSearchUser);
        setFormSearchDateMin(defaultSearchDateMin);
        setFormSearchTimeMin(defaultSearchTimeMin);
        setFormSearchDateMax(defaultSearchDateMax);
        setFormSearchTimeMax(defaultSearchTimeMax);
        computeWebErrors();
      }
  
      const onSearchSubmit = () => {
        searchNum.current     = formSearchNum     ? formSearchNum     : defaultSearchNum;
        searchNo.current      = formSearchNo      ? formSearchNo      : defaultSearchNo;
        searchUser.current    = formSearchUser    ? formSearchUser    : defaultSearchUser;
        searchDateMin.current = formSearchDateMin ? formSearchDateMin : defaultSearchDateMin;
        searchTimeMin.current = formSearchTimeMin ? formSearchTimeMin : defaultSearchTimeMin;
        searchDateMax.current = formSearchDateMax ? formSearchDateMax : defaultSearchDateMax;
        searchTimeMax.current = formSearchTimeMax ? formSearchTimeMax : defaultSearchTimeMax;
        computeWebErrors();
      }

      const onSearchReset = () => {
        searchReset();
        formSearchReset();
        forceSearchRefresh();
      }

      const onToggleSearchExpanded = (_event : React.MouseEvent, isExpanded : boolean) => {
        setSearchExpanded(isExpanded);
        forceSearchRefresh();
        refreshHeightDelta();
      };

      const onSearchNumChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {        
        const count = (allWebErrorsCount.computedData.gtweberrors?.count) ? allWebErrorsCount.computedData.gtweberrors.count : null;
        
        setFormSearchNum(
          value !== undefined && value !== null && value !== "Infinity"
            ? parseInt(value)
            : count !== null
            ? count
            : defaultSearchNum
        );
      }

      const onSearchNoChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchNo((value !== undefined && value !== null) ? parseInt(value) : defaultSearchNo);
      }

      const onSearchUserChange = (_event : React.FormEvent<HTMLInputElement>, value : string) => {
        setFormSearchUser((value !== undefined && value !== null) ? value : defaultSearchUser);
      }


      async function retrievWebErrorsRestartDate(newDateArg? : any) : Promise<any> {        
        const args : any = {"_attributes" : "_none, uri","_count" : 0, "_num" : 1, "_start" : 0};
        const mergedArgs = { ...args, ...newDateArg };
        await refreshJolokia();

        let json : any = null;
        if (jolokia !== null) {
            const response = await jolokia.search(adminPath);
            if (response != null && response.length > 0) {
              const mbean = response[0];
              const operation = retrieveWebErrorsSignature;
              log.debug(logPrefix, "retrievWebErrors, execute : ", JSON.stringify(mergedArgs));
              const result = await jolokia.execute(mbean, operation, JSON.stringify(mergedArgs));  
              json = (result === null) ? null : JSON.parse(result.toString());
            }
        }

        log.info(logPrefix, "retrievWebErrorsRestartDate, json: ", json);

        let restart_date = (json?.gtweberrors?.items === undefined || json?.gtweberrors?.items === null ||json?.gtweberrors?.items[0] === undefined || json?.gtweberrors?.items[0] === null) ? null : json.gtweberrors.items[0].startDate;
        log.info(logPrefix, "retrievWebErrorsRestartDate, restart_date: ", restart_date);
        
        setFormRestartDate(restart_date);
        
        log.info(logPrefix, "retrievWebErrorsRestartDate, formRestartDate: ", formRestartDate);
      }

      useEffect(() => {
        if (formRestartDate !== null) {
          log.info(logPrefix, "formRestartDate has been updated: ", formRestartDate);
          onToday(); 
        }
      }, [formRestartDate]);


      const onToday = () => {
        const today = new Date();

        let currentDayStart = new Date();
        let currentDayEnd = new Date();

        if (typeof formRestartDate === "number") {
          //const args : any = { "restart_date" : formRestartDate, "_attributes" : "step_counter, error_counter, warning_counter" }
          handleFormSearchDateMax(JulianDateTime.julianToDate(formRestartDate));
          searchDateMax.current = JulianDateTime.julianToDate(formRestartDate);
          handleFormSearchDateMin(JulianDateTime.julianToDate(formRestartDate));
          searchDateMin.current = JulianDateTime.julianToDate(formRestartDate);
          computeWebErrors()
        }
        else {
          currentDayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          currentDayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate());

          handleFormSearchDateMin(currentDayStart);
          handleFormSearchDateMax(currentDayEnd);

          searchDateMin.current = currentDayStart;
          searchDateMax.current = currentDayEnd;
          computeWebErrors();
        }
      };

      const onPrevious = () => {
        let julianDateMax : number | null;
        let newDateMax : number | null;
        if (searchDateMax.current instanceof Date && !isNaN(searchDateMax.current.getTime())){
          julianDateMax = JulianDateTime.dateToJulian(searchDateMax.current);
          newDateMax = julianDateMax - 1;
        }
        else {
          julianDateMax = JulianDateTime.dateToJulian(new Date());
          newDateMax = julianDateMax - 1;
        }
        const arg : any = {"date_max" : newDateMax, "_sort" : "-startDate"};
        retrievWebErrorsRestartDate(arg);

      };

      const onNext = () => {
        let julianDateMin : number | null;
        let newDateMin : number | null;
        if (searchDateMin.current instanceof Date && !isNaN(searchDateMin.current.getTime())) {
          julianDateMin = JulianDateTime.dateToJulian(searchDateMin.current);
          newDateMin = julianDateMin + 1;
          const arg : any = {"date_min" : newDateMin, "_sort" : "startDate"};
          retrievWebErrorsRestartDate(arg);
        }
        else {
          onToday();
        }    

      };

      const PrevNextTodayButtons = () => {
        return (
          <>
            <GridItem tabIndex = {-1} span = {2}/>
            <GridItem tabIndex = {-1} span = {1}>
              <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onPrevious()}
                  >
                    &lt; Prev.
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onNext()}
                  >
                    Next &gt;
                  </Button>
                </SplitItem>
                <SplitItem tabIndex = {-1}>
                  <Button
                    className = "pagination-button"
                    variant   = "primary"
                    size      = "sm"
                    onClick   = {() => onToday()}
                  >
                    Today
                  </Button>
                </SplitItem>
              </Split>
            </GridItem>
          </>
        );
      } 

      const selectedNumbersLabel = formNumbersList.find((j) => j.value === formSearchNum)?.content || "All";

      const onFormNumbersSelect = (_event : React.MouseEvent<Element, MouseEvent> | undefined, value : string | number | undefined) => {
        if (typeof value === "number") {
          onSearchNumChange({} as React.FormEvent<HTMLInputElement>, value.toString());
        }
        setIsNumbersDropdownOpen(false);
      };

      return (
        <div style = {{position : 'relative'}}>
          <ExpandableSection toggleText = "Search Criteria" isIndented isExpanded = {isSearchExpanded()} onToggle = {onToggleSearchExpanded}>
            <Grid tabIndex = {-1} hasGutter = {false}>
              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>Number of errors</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <Dropdown
                      isOpen        = {isNumbersDropdownOpen}
                      onSelect      = {onFormNumbersSelect}
                      onOpenChange  = {(isOpen : boolean) => setIsNumbersDropdownOpen(isOpen)}
                      toggle        = {(toggleRef : React.Ref<MenuToggleElement>) => (
                        <MenuToggle className = "font-size-13" ref = {toggleRef} onClick = {() => setIsNumbersDropdownOpen(!isNumbersDropdownOpen)} isExpanded = {isNumbersDropdownOpen}>
                          {selectedNumbersLabel}
                        </MenuToggle>
                      )}
                      ouiaId        = "JobsDropdown"
                      shouldFocusToggleOnSelect
                    >
                      <DropdownList>
                        {
                          formNumbersList.map((option, index) => (
                            <DropdownItem key = {index} value = {option.value}>
                              {option.content}
                            </DropdownItem>
                          ))
                        }
                      </DropdownList>
                    </Dropdown>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {4}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "font-size-13" component = {TextVariants.p}>{getAllWebErrorsCount()}</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text tabIndex = {-1} className = "search_box_titles_size_padding" style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>Date between</Text>
                    </TextContent>
                  </GridItem>                  
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <WebErrorDate id = "searchDateMin" date = {formSearchDateMin} dateHandler = {handleFormSearchDateMin}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <WebErrorTime id = "searchTimeMin" time = {formSearchTimeMin} timeHandler = {handleFormSearchTimeMin}/>
                      </SplitItem>
                    </Split>
                  </GridItem>
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem>

              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>No</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextInput
                      className   = "font-size-13"
                      id          = "searchNo"
                      type        = "number"
                      value       = {formSearchNo ? formSearchNo : ""}
                      onChange    = {onSearchNoChange}
                    />
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {4}/>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "search_box_titles_size_padding_60" tabIndex = {-1} style = {{whiteSpace : 'nowrap'}} component = {TextVariants.p}>and</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {1}>
                    <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                      <SplitItem tabIndex = {-1}>
                        <WebErrorDate id = "searchDateMax" date = {formSearchDateMax} dateHandler = {handleFormSearchDateMax}/>
                      </SplitItem>
                      <SplitItem tabIndex = {-1}>
                        <WebErrorTime id = "searchTimeMax" time = {formSearchTimeMax} timeHandler = {handleFormSearchTimeMax}/>
                      </SplitItem>
                    </Split>
                  </GridItem>
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem>

              <GridItem tabIndex = {-1} span = {8}>
                <Grid tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <GridItem tabIndex = {-1} span = {2}>
                    <TextContent tabIndex = {-1}>
                      <Text className = "font-size-13" tabIndex = {-1} component = {TextVariants.p}>User</Text>
                    </TextContent>
                  </GridItem>
                  <GridItem tabIndex = {-1} span = {6}>
                    <TextInput
                      className   = "font-size-13"
                      id          = "searchUser"
                      type        = "text"
                      value       = {formSearchUser ? formSearchUser : ""}
                      onChange    = {onSearchUserChange}
                    />
                  </GridItem>
                  {isSearchExpanded() && <PrevNextTodayButtons/>}
                </Grid>
              </GridItem>
              <GridItem tabIndex = {-1} span = {4}>
              </GridItem>
              <GridItem tabIndex = {-1} span = {12}>
                <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
                  <SplitItem tabIndex = {-1} isFilled >
                  </SplitItem>
                  <SplitItem tabIndex = {-1} >
                    <Button
                      className = "font-size-13"
                      variant   = "primary"
                      size      = "sm"
                      onClick   = {() => onSearchSubmit()}
                      icon      = {<SearchIcon className = "font-size-13"/>}
                    >
                      Search
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                    <Button
                      className = "font-size-13"
                      variant   = "secondary"
                      size      = "sm"
                      onClick   = {() => onSearchReset()}
                      icon      = {<EraserIcon className = "font-size-13"/>}
                    >
                      Reset
                    </Button>
                  </SplitItem>
                  <SplitItem tabIndex = {-1}>
                  </SplitItem>
                </Split>
              </GridItem>

            </Grid>
          </ExpandableSection>
            <div className = "top-right-corner">
              <div style = {{position : 'relative'}}>
                {!isSearchExpanded() && (
                  <div className = "prev-next-today-container">
                    <PrevNextTodayButtons/>
                  </div>
                )}
              </div>            
            </div>
        </div>
      )
    }

    const WebErrorsTable = () => {
      return (
        <div>
          <Divider tabIndex = {-1} component = "hr" className = "padding-top-10"/>
          <Split tabIndex = {-1} hasGutter style = {{alignItems : "center"}}>
            <SplitItem tabIndex = {-1} className = "padding-left-10">
              <TextContent tabIndex = {-1}>
                <Text tabIndex = {-1}  className = "font-size-13">Selected Web Errors:</Text>
              </TextContent>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <Badge tabIndex = {-1}  className = "font-size-12">{selectedWebErrorsItemsNums.length}</Badge>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <WebErrorsDelete/>
            </SplitItem>
            <SplitItem tabIndex = {-1}>
              <WebErrorsDownload/>
            </SplitItem>
            <SplitItem tabIndex = {-1} isFilled>
              <WebErrorsPagination/>
            </SplitItem>
          </Split>
          <GraphTalkComponentDiv delta = {heightDelta}>
            <InnerScrollContainer>
              <Table 
                tabIndex    = {-1}
                className   = "custom-compact-table"
                isStriped   = {sortedWebErrorsItems.length > 0} 
                aria-label  = "Web Errors table" 
                variant     = "compact" 
                borders     = {false} 
                isStickyHeader
              >
                <Thead tabIndex = {-1}>
                  <Tr tabIndex = {-1}>
                    <Th
                      tabIndex    = {-1}
                      select      = {{
                        onSelect    : (_event, isSelecting) => selectAllWebErrorsItems(isSelecting),
                        isSelected  : areSeveralWebErrorsItemsSelected()
                      }}
                      aria-label        = "Web Errors Table Select "
                      isStickyColumn    = {(stickyColumn >= 0)}
                      stickyMinWidth    = {(stickyColumn >= 0) ? "auto" : undefined}
                      stickyLeftOffset  = {(stickyColumn >= 0) ? "0px"  : undefined}
                      />
                    {
                      webErrorsItemsMap.map((column : any, columnIndex : number) =>
                        <Th
                          tabIndex          = {-1}
                          key               = {column.key}
                          className         = "pf-v5-c-table__th truncate-header"
                          isStickyColumn    = {(columnIndex === stickyColumn)}
                          stickyMinWidth    = {(columnIndex === stickyColumn) ? "auto" : undefined}
                          stickyLeftOffset  = {(columnIndex === stickyColumn) ? "0px"  : undefined}
                          hasRightBorder    = {(columnIndex === stickyColumn)}
                          sort              = {getSortParams(columnIndex)}
                          modifier          = "truncate"
                          style             = {{justifyItems : column.align}}
                        >
                          {column.header}
                        </Th>
                      )
                    }
                  </Tr>
                </Thead>
                <Tbody tabIndex = {-1}>
                {
                  (sortedWebErrorsItems.length > 0) ?
                    sortedWebErrorsItems.slice(indexStart, indexEnd).map((webErrorItem : any, webErrorItemIndex : number) =>
                      <WebErrorItem tabIndex = {-1} key = {webErrorItemIndex} webErrorItem = {webErrorItem} rowIndex = {webErrorItemIndex}/>)
                     :
                      <Tr tabIndex = {-1}>
                        <Td tabIndex = {-1} colSpan = {webErrorsItemsMap.length + 1}>
                          <Bullseye tabIndex = {-1}>
                            <EmptyState tabIndex = {-1} variant = {EmptyStateVariant.xs}>
                              <EmptyStateHeader tabIndex = {-1} titleText = "No Web Errors Found" icon = {<EmptyStateIcon icon = {SearchIcon} className = "font-size-14"/>}/>
                              <EmptyStateBody tabIndex = {-1}>
                              </EmptyStateBody>
                            </EmptyState>
                          </Bullseye>
                        </Td>
                      </Tr>
                }
                </Tbody>
              </Table>
            </InnerScrollContainer>
          </GraphTalkComponentDiv>
        </div>
      )

    }

    // ********************************************************************************************************

    const webErrorsCount = () : any => {
      const count = (webErrors.computedData.gtweberrors?.count) ? webErrors.computedData.gtweberrors.count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+") 
          return "at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"
        else
          return scount + " items";
      }
      else
        return ""
    }    

    const getAllWebErrorsCount = () : any => {
      const count = (allWebErrorsCount.computedData.gtweberrors?.count) ? allWebErrorsCount.computedData.gtweberrors.count : null;
      if (count !== null) {
        const scount = count.toString();
        if (count !== null && scount[scount.length - 1] === "+") 
            return "from at least " + scount.substring(0, Math.max(0, scount.length - 1)) + " items"                  
        else
          if (count < 500)
            return "from " + scount + " items";
          else
          return "from at least " + scount + " items";
      }
      else
        return ""
    }

    // ********************************************************************************************************

    const [page,       setPage      ] = useState<number | null>(1);
    const [indexStart, setIndexStart] = useState<number | null>(0);
    const [indexEnd,   setIndexEnd  ] = useState<number | null>(getPerPage());
    const [refreshPagintion, setRefreshPagination] = useState<boolean>(false);

    const toggleRefreshPagination = () => {
      setRefreshPagination(!refreshPagintion);
    }
  
    const WebErrorsPagination = () => {
      
      const handleSetPage = (
        _event      : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPage     : number,
        _perPage    : number | undefined,
        startIdx    : number | undefined,
        endIdx      : number | undefined
      ) => {
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
      };
  
      const handlePerPageSelect = (
        _event      : React.MouseEvent | React.KeyboardEvent | MouseEvent,
        newPerPage  : number,
        newPage     : number,
        startIdx    : number | undefined,
        endIdx      : number | undefined
      ) => {
        setPerPage(newPerPage);
        setPage(newPage);
        setIndexStart((startIdx !== undefined) ? startIdx : null);
        setIndexEnd((endIdx !== undefined) ? endIdx : null);
        toggleRefreshPagination();
      };

      return (
        <Pagination
          isCompact       = {false}
          itemCount       =  {sortedWebErrorsItems.length}
          toggleTemplate  = {({firstIndex, lastIndex}) => (<><b>{firstIndex} - {lastIndex}</b> of <b>{webErrorsCount()}</b></>)}
          perPage         = {getPerPage()}
          page            = {(page !== null) ? page : undefined}
          onSetPage       = {handleSetPage}
          onPerPageSelect = {handlePerPageSelect}
        />
      )
    }

    // ********************************************************************************************************

    async function deleteWebError (webErrorNum : any) {
      let jolokia = null;
      try {
        jolokia = await jolokiaService.getJolokia();
        log.debug(logPrefix, "retrieveJolokia, ok: ", jolokia);
      }
      catch (error) {
        log.error(logPrefix, "retrieveJolokia, error: ", error);
      }
  
      if (jolokia !== null) {
        const response = await jolokia.search(adminPath);
        if (response != null && response.length > 0) {
          const mbean = response[0];
          const operation = "deleteWebError(java.lang.Integer)";
          const result = await jolokia.execute(mbean, operation, webErrorNum);
          log.debug(logPrefix, "deleteWebError, result: ", result);
        }
      }
    }

    // ********************************************************************************************************

    async function deleteSelectedWebErrors () {
      if (!areSeveralWebErrorsItemsSelected()) return;

      for (let selectedWebErrorsItemsNumsIndex = 0; selectedWebErrorsItemsNumsIndex < selectedWebErrorsItemsNums.length; selectedWebErrorsItemsNumsIndex++) {
        const webErrorNum = selectedWebErrorsItemsNums[selectedWebErrorsItemsNumsIndex];
        await deleteWebError(webErrorNum);
      }

      await computeWebErrors();
    }

    // ********************************************************************************************************

    const WebErrorsDelete = () => {

      const isDisabled = () : boolean => {
        return !context?.canDeleteWebError || !areSeveralWebErrorsItemsSelected();
      }

      if (isDisabled())
        return <></>;

      const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

      const handleModalOpen = () => {
        setIsModalOpen(!isModalOpen);
      }

      const handleModalDelete = () => {
        deleteSelectedWebErrors();
        setIsModalOpen(false);
      }

      const handleModalCancel = () => {
        setIsModalOpen(false);
      }

      return (
        <>
          <Tooltip content = {"Delete the selected Web Error(s)"}>
            <Button
              isDisabled  = {isDisabled()}
              variant     = "plain"
              size        = "default"
              onClick     = {handleModalOpen}
              icon        = {<Icon status = "danger" iconSize = "md"><TrashIcon/></Icon>}
            />
          </Tooltip>
          <Modal
            titleIconVariant      = "danger"
            variant               = {ModalVariant.small}
            isOpen                = {isModalOpen}
            onClose               = {handleModalCancel}
            title                 = {"Delete the " + ((selectedWebErrorsItemsNums.length > 1) ? selectedWebErrorsItemsNums.length + " selected Web Errors ?" : " selected Web Error ?")}
            actions               = {[
              <Button key = "confirm" variant = "danger"    isBlock size = "sm" onClick = {handleModalDelete}>Delete</Button>,
              <Button key = "cancel"  variant = "secondary" isBlock size = "sm" onClick = {handleModalCancel}>Cancel</Button>
            ]}
          >
            <Title tabIndex = {-1} headingLevel = "h4" size = "md">
              {((selectedWebErrorsItemsNums.length > 1)
                ? "All the " + selectedWebErrorsItemsNums.length + " selected Web Errors will be permanently deleted"
                : "The selected Web Error will be permanently deleted")}
            </Title>
          </Modal>
        </>
      )
    }

    // ********************************************************************************************************

    const formatWebErrorAsText = (webErrorJson : any) : String[] => {
			const webErrorText : String[] = [];

      try {
        webErrorText.push(" ------------------------------- \n");
        webErrorText.push("|  Graphtalk Application Error  |\n");
        webErrorText.push(" ------------------------------- \n\n");

        const gtweberror = webErrorJson.gtweberror;

        webErrorText.push (
            "The error #" + gtweberror.num + " was reported by " + gtweberror.userId
          + " on " + JulianDateTime.formatJulianAsDate(gtweberror.startDate)
          + " at " + JulianDateTime.formatJulianAsTime(gtweberror.startTime)
          + "\n"
        );
        webErrorText.push ("Language: " + gtweberror.language + "\n");
        webErrorText.push ("Level: " + gtweberror.level + "\n");
        webErrorText.push ("Inner Error Number: " + gtweberror.inner_error_number + "\n\n");

        const webErrorBody = (gtweberror?.detail) ? gtweberror.detail : null;
        const webErrorBodyLength = webErrorBody.length;

        for (let webErrorMsgIndex = webErrorBodyLength - 1; webErrorMsgIndex >= 0; webErrorMsgIndex--) {
          const webErrorMsg       = webErrorBody[webErrorMsgIndex]
          const webErrorMsgHeader = webErrorMsg[0];
          const webErrorMsgBody   = webErrorMsg[1];

          webErrorText.push (webErrorMsgHeader + "\n");
          webErrorText.push ("-------------------------------------------------------------------------------\n");

          for (let webErrorMsgBodyIndex = 0; webErrorMsgBodyIndex < webErrorMsgBody.length; webErrorMsgBodyIndex++) {
            webErrorText.push(webErrorMsgBody[webErrorMsgBodyIndex] + "\n");
          }

          webErrorText.push("\n");
        };

        webErrorText.push ("-------------------------------------------------------------------------------\n");
        webErrorText.push ("-------------------------------------------------------------------------------\n");
        webErrorText.push ("Related Request: " + gtweberror.request + "\n");
      }
      catch (error) {
				webErrorText.push ("ERROR IN JSON: " + error + "\n\n");
				webErrorText.push (webErrorJson.toString());
				webErrorText.push ("\n");
      }

      return webErrorText;
    }

    // ********************************************************************************************************

    const WebErrorsDownload = () => {

      if (selectedWebErrorsItemsNums.length !== 1)
          return (<></>);

      const webErrorNum = selectedWebErrorsItemsNums[0];
   
      async function downloadWebErrorAsText() : Promise<any> {
        let jolokia = null;
        try {
          jolokia = await jolokiaService.getJolokia();
        }
        catch (error) {
          log.error(logPrefix, "WebErrorsDownload retrieveJolokia, error: ", error);
        }
      
        let webErrorJson : any = null;

        if (jolokia !== null) {
          const response = await jolokia.search(adminPath);
          if (response != null && response.length > 0) {
            const mbean = response[0];
            const operation = "retrieveWebError(java.lang.Integer)";
            const result = await jolokia.execute(mbean, operation, webErrorNum);
            webErrorJson = (result === undefined || result === null) ? null : JSON.parse(result.toString());
          }
        }

        if (webErrorJson !== null) {
          const webErrorText : any[] = formatWebErrorAsText(webErrorJson);
          const webErrorBlob = new Blob(webErrorText, {type : "text/plain"});
          const webErrorUrl = URL.createObjectURL(webErrorBlob);
          const webErrorLink = document.createElement("a");
          webErrorLink.download = "weberror-" + webErrorNum + ".txt";
          webErrorLink.href = webErrorUrl;
          webErrorLink.click();
        }
      }

      const handleDownload = () => {
        downloadWebErrorAsText()
          .then  (()    => {
            log.debug(logPrefix, "WebErrorsDownload downloadWebErrorAsText OK");
          })
          .catch (error => {
            log.debug(logPrefix, "WebErrorsDownload downloadWebErrorAsText exception: ", error);
          })
      }

      return (
        <Tooltip content = {"Download the selected Web Error"}>
          <Button
            isDisabled  = {selectedWebErrorsItemsNums.length !== 1}
            variant     = "plain"
            size        = "default"
            onClick     = {handleDownload}
            icon        = {<Icon status = "info" iconSize = "md"><DownloadIcon/></Icon>}
          />
        </Tooltip>
      )
    }

    // ********************************************************************************************************

    return (
      <Drawer tabIndex = {-1} isInline isExpanded = {true}>
        <DrawerContent tabIndex = {-1} panelContent = {webErrorsPanelContent} style = {{paddingTop : "0px"}}>
          <WebErrorsSearch/>
          <WebErrorsTable/>
        </DrawerContent>
      </Drawer>
    )
  }

  // ********************************************************************************************************

  const [context, setContext] = useState<any | null>(null);

  async function retrieveContext() {
    const context = await getContext();
    setContext(context);
    log.debug(logPrefix, "context: ", context);
  }

  // ********************************************************************************************************

  async function computeWebErrors() {
    await retrieveContext();

    await refreshJolokia();

    let webErrorsNew = {...webErrors};
    let allWebErrorsCountNew = {...allWebErrorsCount};

    await retrieveWebErrors(webErrorsNew);
    await retrieveAllWebErrorsCount(allWebErrorsCountNew);

    try {
      webErrorsNew.computedData = webErrorsNew.retrievedData;
      allWebErrorsCountNew.computedData = allWebErrorsCountNew.retrievedData;
    } catch(e) {}

    setWebErrors(webErrorsNew);
    setAllWebErrorsCount(allWebErrorsCountNew);
  }

  // ********************************************************************************************************

  log.debug(logPrefix, "Displaying GtMonitor ****************************************************************************");

  return (
    <GraphTalkComponent tabIndex = {-1} title = "Web Errors" onCompute = {computeWebErrors}>
      <WebErrorsItems/>
    </GraphTalkComponent>
  )
}

export default WebErrors;